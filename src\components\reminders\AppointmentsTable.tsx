import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { ActionButtons } from './ActionButtons';
import { formatDate, formatAppointmentType } from '@/lib/formatters';
import { AppointmentType } from '@/mocks/enums';

interface Appointment {
  id: string;
  type: AppointmentType;
  dateTime: Date;
  provider: string;
}

interface AppointmentsTableProps {
  appointments: Appointment[];
  onAction: (appointmentId: string, action: string) => void;
}

export function AppointmentsTable({ appointments, onAction }: AppointmentsTableProps) {
  return (
    <div className="health-table-container">
      <Table>
        <TableHeader>
          <TableRow className="health-table-header">
            <TableHead className="text-[18px] font-normal font-lato text-[#191919] px-10">Appointment Type</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Date & Time</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Provider</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {appointments.map((appointment) => (
            <TableRow key={appointment.id} className="health-table-row">
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b] px-10">
                {formatAppointmentType(appointment.type)}
              </TableCell>
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b]">
                {formatDate(appointment.dateTime)}
              </TableCell>
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b]">{appointment.provider}</TableCell>
              <TableCell>
                <ActionButtons
                  itemId={appointment.id}
                  type="appointment"
                  onAction={onAction}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}