import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ConditionOverviewProps {
  condition: string;
  diagnosisDate: string;
  lastReview: string;
  status: 'improving' | 'stable' | 'declining';
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long' 
  });
};

const formatReviewDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};

export function ConditionOverview({ 
  condition, 
  diagnosisDate, 
  lastReview, 
  status 
}: ConditionOverviewProps) {
  return (
    <div className="space-y-4">
      <h2 className="health-heading-md">Condition Overview</h2>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <p className="health-body-lg text-health-accent">Condition</p>
          <p className="health-heading-sm text-health-accent">{condition}</p>
        </div>
        
        <div className="space-y-2">
          <p className="health-body-lg text-health-accent">Diagnosis Date</p>
          <p className="health-heading-sm text-health-accent">{formatDate(diagnosisDate)}</p>
        </div>
        
        <div className="space-y-2">
          <p className="health-body-lg text-health-accent">Last Review</p>
          <p className="health-heading-sm text-health-accent">{formatReviewDate(lastReview)}</p>
        </div>
        
        <div className="space-y-2">
          <p className="health-body-lg text-health-accent">Status</p>
          <Badge 
            variant="secondary" 
            className="bg-[#c8e6f0] text-health-secondary text-xs font-medium px-3 py-1 rounded-full"
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        </div>
      </div>
    </div>
  );
}