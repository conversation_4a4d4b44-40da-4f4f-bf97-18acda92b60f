{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ChevronRightIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 7.006 13.341\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" d=\"M.189 1.125a.66.66 0 0 1-.124-.73.68.68 0 0 1 .371-.351.67.67 0 0 1 .722.165l5.667 6a.667.667 0 0 1 0 .916l-5.667 6a.67.67 0 0 1-.726.173.67.67 0 0 1-.374-.353.67.67 0 0 1 .131-.735l5.234-5.543z\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,8OAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAoB,GAAG,KAAK;kBAAE,cAAA,8OAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,GAAE;;;;;;;;;;;uCAChM", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/reminders/BreadcrumbNavigation.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  B<PERSON><PERSON><PERSON><PERSON>,\n  B<PERSON><PERSON><PERSON><PERSON><PERSON>ist,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbSeparator,\n  BreadcrumbPage,\n} from '@/components/ui/breadcrumb';\nimport ChevronRightIcon from '@/components/icons/ChevronRightIcon';\n\ninterface BreadcrumbItem {\n  label: string;\n  href?: string;\n}\n\ninterface BreadcrumbNavigationProps {\n  items: BreadcrumbItem[];\n}\n\nexport function BreadcrumbNavigation({ items }: BreadcrumbNavigationProps) {\n  return (\n    <Breadcrumb>\n      <BreadcrumbList className=\"text-[18px] font-normal font-lato text-[#868686] flex items-center gap-2\">\n        {items.map((item, index) => (\n          <React.Fragment key={index}>\n            <BreadcrumbItem>\n              {index === items.length - 1 ? (\n                <BreadcrumbPage className=\"text-[18px] font-normal font-lato text-[#868686]\">{item.label}</BreadcrumbPage>\n              ) : (\n                <BreadcrumbLink href={item.href} className=\"text-[18px] font-normal font-lato text-[#868686]\">\n                  {item.label}\n                </BreadcrumbLink>\n              )}\n            </BreadcrumbItem>\n            {index < items.length - 1 && (\n              <BreadcrumbSeparator>\n                <ChevronRightIcon width={7} height={13} color=\"#868686\" />\n              </BreadcrumbSeparator>\n            )}\n          </React.Fragment>\n        ))}\n      </BreadcrumbList>\n    </Breadcrumb>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;;;;;AAWO,SAAS,qBAAqB,EAAE,KAAK,EAA6B;IACvE,qBACE,8OAAC,sIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;YAAC,WAAU;sBACvB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;sCACb,8OAAC,sIAAA,CAAA,iBAAc;sCACZ,UAAU,MAAM,MAAM,GAAG,kBACxB,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CAAoD,KAAK,KAAK;;;;;qDAExF,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,IAAI;gCAAE,WAAU;0CACxC,KAAK,KAAK;;;;;;;;;;;wBAIhB,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC,sIAAA,CAAA,sBAAmB;sCAClB,cAAA,8OAAC,+IAAA,CAAA,UAAgB;gCAAC,OAAO;gCAAG,QAAQ;gCAAI,OAAM;;;;;;;;;;;;mBAZ/B;;;;;;;;;;;;;;;AAoB/B", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/chronic-condition/ConditionOverview.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\n\ninterface ConditionOverviewProps {\n  condition: string;\n  diagnosisDate: string;\n  lastReview: string;\n  status: 'improving' | 'stable' | 'declining';\n}\n\nconst formatDate = (dateString: string): string => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', { \n    year: 'numeric', \n    month: 'long' \n  });\n};\n\nconst formatReviewDate = (dateString: string): string => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', { \n    year: 'numeric', \n    month: 'long', \n    day: 'numeric' \n  });\n};\n\nexport function ConditionOverview({ \n  condition, \n  diagnosisDate, \n  lastReview, \n  status \n}: ConditionOverviewProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-md\">Condition Overview</h2>\n      \n      <div className=\"space-y-4\">\n        <div className=\"space-y-2\">\n          <p className=\"health-body-lg text-health-accent\">Condition</p>\n          <p className=\"health-heading-sm text-health-accent\">{condition}</p>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <p className=\"health-body-lg text-health-accent\">Diagnosis Date</p>\n          <p className=\"health-heading-sm text-health-accent\">{formatDate(diagnosisDate)}</p>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <p className=\"health-body-lg text-health-accent\">Last Review</p>\n          <p className=\"health-heading-sm text-health-accent\">{formatReviewDate(lastReview)}</p>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <p className=\"health-body-lg text-health-accent\">Status</p>\n          <Badge \n            variant=\"secondary\" \n            className=\"bg-[#c8e6f0] text-health-secondary text-xs font-medium px-3 py-1 rounded-full\"\n          >\n            {status.charAt(0).toUpperCase() + status.slice(1)}\n          </Badge>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;;;AASA,MAAM,aAAa,CAAC;IAClB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;IACT;AACF;AAEA,MAAM,mBAAmB,CAAC;IACxB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,kBAAkB,EAChC,SAAS,EACT,aAAa,EACb,UAAU,EACV,MAAM,EACiB;IACvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAGvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAwC,WAAW;;;;;;;;;;;;kCAGlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAwC,iBAAiB;;;;;;;;;;;;kCAGxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;0CAET,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/chronic-condition/PlanOverview.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\n\nconst keyStrategies = [\n  \"Pacing & Energy Management - Optimize daily activities to prevent overexertion\",\n  \"Medication Adherence - Consistent preventive treatment with rescue options\", \n  \"Trigger Identification - Systematic tracking and avoidance of personal triggers\",\n  \"Progress Monitoring - Regular assessment of symptoms and plan effectiveness\"\n];\n\nexport function PlanOverview() {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-sm\">Your Plan Overview</h2>\n      \n      <Card className=\"health-card-shadow health-rounded bg-white p-6\">\n        <CardContent className=\"p-0 space-y-6\">\n          <p className=\"health-body-lg text-health-accent leading-relaxed\">\n            Your Personalized migraine management plan focuses on proactive prevention through \n            lifestyle optimization, trigger avoidance, and strategic medication use. The plan \n            emphasizes sustainable pacing strategies to maintain energy levels while reducing \n            migraine frequency and severity\n          </p>\n          \n          <div className=\"space-y-4\">\n            <h3 className=\"health-body-lg text-health-accent font-medium\">Key Strategies</h3>\n            <div className=\"space-y-2\">\n              {keyStrategies.map((strategy, index) => (\n                <p key={index} className=\"health-caption text-health-muted\">\n                  {strategy}\n                </p>\n              ))}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAOjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgD;;;;;;8CAC9D,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC;4CAAc,WAAU;sDACtB;2CADK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/chronic-condition/TabNavigation.tsx"], "sourcesContent": ["import React from 'react';\nimport { Tabs, Tabs<PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';\n\nconst tabItems = [\n  { id: 'pacing', label: 'Pacing & Activity' },\n  { id: 'medication', label: 'Medication Regimen' },\n  { id: 'trigger', label: 'Trigger Management' },\n  { id: 'progress', label: 'Progress Tracking' }\n];\n\nexport function TabNavigation() {\n  return (\n    <Tabs defaultValue=\"pacing\" className=\"w-full\">\n      <TabsList className=\"grid w-full grid-cols-4 bg-transparent p-0 h-auto\">\n        {tabItems.map((tab) => (\n          <TabsTrigger \n            key={tab.id}\n            value={tab.id}\n            className=\"health-body-lg text-health-muted data-[state=active]:text-health-accent data-[state=active]:border-b-2 data-[state=active]:border-health-accent rounded-none bg-transparent px-0 py-2\"\n          >\n            {tab.label}\n          </TabsTrigger>\n        ))}\n      </TabsList>\n      \n      {tabItems.map((tab) => (\n        <TabsContent key={tab.id} value={tab.id} className=\"mt-4\">\n          {/* Tab content will be rendered here based on active tab */}\n        </TabsContent>\n      ))}\n    </Tabs>\n  );\n}"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,WAAW;IACf;QAAE,IAAI;QAAU,OAAO;IAAoB;IAC3C;QAAE,IAAI;QAAc,OAAO;IAAqB;IAChD;QAAE,IAAI;QAAW,OAAO;IAAqB;IAC7C;QAAE,IAAI;QAAY,OAAO;IAAoB;CAC9C;AAEM,SAAS;IACd,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,cAAa;QAAS,WAAU;;0BACpC,8OAAC,gIAAA,CAAA,WAAQ;gBAAC,WAAU;0BACjB,SAAS,GAAG,CAAC,CAAC,oBACb,8OAAC,gIAAA,CAAA,cAAW;wBAEV,OAAO,IAAI,EAAE;wBACb,WAAU;kCAET,IAAI,KAAK;uBAJL,IAAI,EAAE;;;;;;;;;;YAShB,SAAS,GAAG,CAAC,CAAC,oBACb,8OAAC,gIAAA,CAAA,cAAW;oBAAc,OAAO,IAAI,EAAE;oBAAE,WAAU;mBAAjC,IAAI,EAAE;;;;;;;;;;;AAMhC", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/chronic-condition/ActivityGuidance.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\n\ninterface ActivityGuidanceProps {\n  recommendedLevel: string;\n  description: string;\n  restBreaks: string;\n  restDescription: string;\n}\n\nexport function ActivityGuidance({ \n  recommendedLevel, \n  description, \n  restBreaks, \n  restDescription \n}: ActivityGuidanceProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-sm\">Pacing & Activity Guidance</h2>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <Card className=\"bg-white p-6\">\n          <CardContent className=\"p-0 space-y-2\">\n            <p className=\"health-caption text-health-accent\">Today's Recommended Activity Level</p>\n            <p className=\"health-heading-sm text-health-accent font-medium\">{recommendedLevel}</p>\n            <p className=\"health-body-sm text-health-accent\">{description}</p>\n          </CardContent>\n        </Card>\n        \n        <Card className=\"bg-white p-6\">\n          <CardContent className=\"p-0 space-y-2\">\n            <p className=\"health-caption text-health-accent\">Suggested Rest Breaks</p>\n            <p className=\"health-heading-sm text-health-accent font-medium\">{restBreaks}</p>\n            <p className=\"health-body-sm text-health-accent\">{restDescription}</p>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;;;AASO,SAAS,iBAAiB,EAC/B,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,eAAe,EACO;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;kCAItD,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9D", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [k in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction ChartContainer({\r\n  id,\r\n  className,\r\n  children,\r\n  config,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  config: ChartConfig\r\n  children: React.ComponentProps<\r\n    typeof RechartsPrimitive.ResponsiveContainer\r\n  >[\"children\"]\r\n}) {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-slot=\"chart\"\r\n        data-chart={chartId}\r\n        className={cn(\r\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n}\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nfunction ChartTooltipContent({\r\n  active,\r\n  payload,\r\n  className,\r\n  indicator = \"dot\",\r\n  hideLabel = false,\r\n  hideIndicator = false,\r\n  label,\r\n  labelFormatter,\r\n  labelClassName,\r\n  formatter,\r\n  color,\r\n  nameKey,\r\n  labelKey,\r\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n  React.ComponentProps<\"div\"> & {\r\n    hideLabel?: boolean\r\n    hideIndicator?: boolean\r\n    indicator?: \"line\" | \"dot\" | \"dashed\"\r\n    nameKey?: string\r\n    labelKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  const tooltipLabel = React.useMemo(() => {\r\n    if (hideLabel || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const [item] = payload\r\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\r\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n    const value =\r\n      !labelKey && typeof label === \"string\"\r\n        ? config[label as keyof typeof config]?.label || label\r\n        : itemConfig?.label\r\n\r\n    if (labelFormatter) {\r\n      return (\r\n        <div className={cn(\"font-medium\", labelClassName)}>\r\n          {labelFormatter(value, payload)}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (!value) {\r\n      return null\r\n    }\r\n\r\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n  }, [\r\n    label,\r\n    labelFormatter,\r\n    payload,\r\n    hideLabel,\r\n    labelClassName,\r\n    config,\r\n    labelKey,\r\n  ])\r\n\r\n  if (!active || !payload?.length) {\r\n    return null\r\n  }\r\n\r\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\r\n        className\r\n      )}\r\n    >\r\n      {!nestLabel ? tooltipLabel : null}\r\n      <div className=\"grid gap-1.5\">\r\n        {payload.map((item, index) => {\r\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n          const indicatorColor = color || item.payload.fill || item.color\r\n\r\n          return (\r\n            <div\r\n              key={item.dataKey}\r\n              className={cn(\r\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\r\n                indicator === \"dot\" && \"items-center\"\r\n              )}\r\n            >\r\n              {formatter && item?.value !== undefined && item.name ? (\r\n                formatter(item.value, item.name, item, index, item.payload)\r\n              ) : (\r\n                <>\r\n                  {itemConfig?.icon ? (\r\n                    <itemConfig.icon />\r\n                  ) : (\r\n                    !hideIndicator && (\r\n                      <div\r\n                        className={cn(\r\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\r\n                          {\r\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                            \"w-1\": indicator === \"line\",\r\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                              indicator === \"dashed\",\r\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                          }\r\n                        )}\r\n                        style={\r\n                          {\r\n                            \"--color-bg\": indicatorColor,\r\n                            \"--color-border\": indicatorColor,\r\n                          } as React.CSSProperties\r\n                        }\r\n                      />\r\n                    )\r\n                  )}\r\n                  <div\r\n                    className={cn(\r\n                      \"flex flex-1 justify-between leading-none\",\r\n                      nestLabel ? \"items-end\" : \"items-center\"\r\n                    )}\r\n                  >\r\n                    <div className=\"grid gap-1.5\">\r\n                      {nestLabel ? tooltipLabel : null}\r\n                      <span className=\"text-muted-foreground\">\r\n                        {itemConfig?.label || item.name}\r\n                      </span>\r\n                    </div>\r\n                    {item.value && (\r\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\r\n                        {item.value.toLocaleString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nfunction ChartLegendContent({\r\n  className,\r\n  hideIcon = false,\r\n  payload,\r\n  verticalAlign = \"bottom\",\r\n  nameKey,\r\n}: React.ComponentProps<\"div\"> &\r\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n    hideIcon?: boolean\r\n    nameKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  if (!payload?.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex items-center justify-center gap-4\",\r\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n        className\r\n      )}\r\n    >\r\n      {payload.map((item) => {\r\n        const key = `${nameKey || item.dataKey || \"value\"}`\r\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n        return (\r\n          <div\r\n            key={item.value}\r\n            className={cn(\r\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\r\n            )}\r\n          >\r\n            {itemConfig?.icon && !hideIcon ? (\r\n              <itemConfig.icon />\r\n            ) : (\r\n              <div\r\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                style={{\r\n                  backgroundColor: item.color,\r\n                }}\r\n              />\r\n            )}\r\n            {itemConfig?.label}\r\n          </div>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,qMAAA,CAAA,gBAAmB,CAA2B;AAEnE,SAAS;IACP,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OAMJ;IACC,MAAM,WAAW,qMAAA,CAAA,QAAW;IAC5B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,8OAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,8OAAC,mKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;AAEA,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;AAEA,MAAM,eAAe,uJAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EAQP;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,qMAAA,CAAA,UAAa,CAAC;QACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;YACjC,OAAO;QACT;QAEA,MAAM,CAAC,KAAK,GAAG;QACf,MAAM,MAAM,GAAG,YAAY,MAAM,WAAW,MAAM,QAAQ,SAAS;QACnE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;QAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;QAElB,IAAI,gBAAgB;YAClB,qBACE,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAC/B,eAAe,OAAO;;;;;;QAG7B;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,qBAAO,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;sBAAkB;;;;;;IAC7D,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,8OAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,8OAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;AAEA,MAAM,cAAc,sJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EAKN;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,8OAAC,WAAW,IAAI;;;;6CAEhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;AAEA,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/OrangeDotIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 12.77 12\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" fillRule=\"evenodd\" d=\"M6.385 12c3.526 0 6.385-2.69 6.385-6S9.91 0 6.385 0 0 2.69 0 6s2.859 6 6.385 6\" clipRule=\"evenodd\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,8OAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAgB,GAAG,KAAK;kBAAE,cAAA,8OAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,UAAS;YAAU,GAAE;YAAiF,UAAS;;;;;;;;;;;uCACzS", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/PurpleDotIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 12.77 12\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" fillRule=\"evenodd\" d=\"M6.385 12c3.526 0 6.384-2.69 6.384-6S9.911 0 6.385 0 0 2.69 0 6s2.858 6 6.385 6\" clipRule=\"evenodd\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,8OAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAgB,GAAG,KAAK;kBAAE,cAAA,8OAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,UAAS;YAAU,GAAE;YAAkF,UAAS;;;;;;;;;;;uCAC1S", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/chronic-condition/EnergyEnvelopeChart.tsx"], "sourcesContent": ["import React from 'react';\nimport { AreaChart, Area, XAxis, YA<PERSON><PERSON>, ResponsiveContainer, Legend } from 'recharts';\nimport { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';\nimport { Card, CardContent } from '@/components/ui/card';\nimport OrangeDotIcon from '@/components/icons/OrangeDotIcon';\nimport PurpleDotIcon from '@/components/icons/PurpleDotIcon';\n\ninterface EnergyData {\n  day: string;\n  allocatedEnergy: number;\n  usedEnergy: number;\n}\n\ninterface EnergyEnvelopeChartProps {\n  data: EnergyData[];\n}\n\nconst chartConfig = {\n  allocatedEnergy: {\n    label: \"Allocated Energy\",\n    color: \"#f9978a\",\n  },\n  usedEnergy: {\n    label: \"Used Energy\", \n    color: \"#e3b9ff\",\n  },\n};\n\nexport function EnergyEnvelopeChart({ data }: EnergyEnvelopeChartProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-sm\">Energy Envelope - This Week</h2>\n      \n      <Card className=\"health-card-shadow health-rounded bg-white p-6\">\n        <CardContent className=\"p-0\">\n          <ChartContainer config={chartConfig} className=\"h-[400px] w-full\">\n            <AreaChart\n              data={data}\n              margin={{\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 20,\n              }}\n            >\n              <XAxis \n                dataKey=\"day\" \n                axisLine={false}\n                tickLine={false}\n                tick={{ fontSize: 12, fill: '#868686' }}\n              />\n              <YAxis \n                axisLine={false}\n                tickLine={false}\n                tick={{ fontSize: 12, fill: '#868686' }}\n                domain={[0, 100]}\n              />\n              <ChartTooltip content={<ChartTooltipContent />} />\n              <Area\n                type=\"monotone\"\n                dataKey=\"allocatedEnergy\"\n                stackId=\"1\"\n                stroke=\"#f9978a\"\n                fill=\"#f9978a\"\n                fillOpacity={0.8}\n              />\n              <Area\n                type=\"monotone\"\n                dataKey=\"usedEnergy\"\n                stackId=\"1\"\n                stroke=\"#e3b9ff\"\n                fill=\"#e3b9ff\"\n                fillOpacity={0.8}\n              />\n            </AreaChart>\n          </ChartContainer>\n          \n          {/* Custom Legend */}\n          <div className=\"flex items-center justify-center gap-6 mt-4\">\n            <div className=\"flex items-center gap-2\">\n              <OrangeDotIcon width={13} height={12} />\n              <span className=\"health-body-lg text-[#282d32] font-bold\">Allocated Energy</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <PurpleDotIcon width={13} height={12} />\n              <span className=\"health-body-lg text-[#282d32] font-bold\">Used Energy</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAYA,MAAM,cAAc;IAClB,iBAAiB;QACf,OAAO;QACP,OAAO;IACT;IACA,YAAY;QACV,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,EAAE,IAAI,EAA4B;IACpE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,iIAAA,CAAA,iBAAc;4BAAC,QAAQ;4BAAa,WAAU;sCAC7C,cAAA,8OAAC,qJAAA,CAAA,YAAS;gCACR,MAAM;gCACN,QAAQ;oCACN,KAAK;oCACL,OAAO;oCACP,MAAM;oCACN,QAAQ;gCACV;;kDAEA,8OAAC,qJAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,UAAU;wCACV,UAAU;wCACV,MAAM;4CAAE,UAAU;4CAAI,MAAM;wCAAU;;;;;;kDAExC,8OAAC,qJAAA,CAAA,QAAK;wCACJ,UAAU;wCACV,UAAU;wCACV,MAAM;4CAAE,UAAU;4CAAI,MAAM;wCAAU;wCACtC,QAAQ;4CAAC;4CAAG;yCAAI;;;;;;kDAElB,8OAAC,iIAAA,CAAA,eAAY;wCAAC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;;;;;;;;;;kDAC3C,8OAAC,oJAAA,CAAA,OAAI;wCACH,MAAK;wCACL,SAAQ;wCACR,SAAQ;wCACR,QAAO;wCACP,MAAK;wCACL,aAAa;;;;;;kDAEf,8OAAC,oJAAA,CAAA,OAAI;wCACH,MAAK;wCACL,SAAQ;wCACR,SAAQ;wCACR,QAAO;wCACP,MAAK;wCACL,aAAa;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAa;4CAAC,OAAO;4CAAI,QAAQ;;;;;;sDAClC,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;8CAE5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAa;4CAAC,OAAO;4CAAI,QAAQ;;;;;;sDAClC,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/chronicConditionSnapshotMockData.ts"], "sourcesContent": ["// import enums.ts if any\n\n// Data passed as props to the root component\nexport const mockRootProps = {\n  patientData: {\n    condition: \"Migraines\" as const,\n    diagnosisDate: \"2024-03-01\",\n    lastReview: \"2025-06-28\", \n    status: \"improving\" as const\n  },\n  energyData: [\n    { day: \"Mon\", allocatedEnergy: 30, usedEnergy: 25 },\n    { day: \"Tue\", allocatedEnergy: 35, usedEnergy: 40 },\n    { day: \"Wed\", allocatedEnergy: 40, usedEnergy: 35 },\n    { day: \"Thu\", allocatedEnergy: 50, usedEnergy: 45 },\n    { day: \"Fri\", allocatedEnergy: 45, usedEnergy: 35 },\n    { day: \"Sat\", allocatedEnergy: 35, usedEnergy: 30 },\n    { day: \"Sun\", allocatedEnergy: 55, usedEnergy: 50 }\n  ],\n  activityGuidance: {\n    recommendedLevel: \"Light Activity\" as const,\n    description: \"Focus on gentle movement and avoid high-intensity activities\",\n    restBreaks: \"15 min every 2 hours\",\n    restDescription: \"Regular breaks help prevent energy depletion\"\n  }\n};"], "names": [], "mappings": "AAAA,yBAAyB;AAEzB,6CAA6C;;;;AACtC,MAAM,gBAAgB;IAC3B,aAAa;QACX,WAAW;QACX,eAAe;QACf,YAAY;QACZ,QAAQ;IACV;IACA,YAAY;QACV;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;QAClD;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;QAClD;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;QAClD;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;QAClD;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;QAClD;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;QAClD;YAAE,KAAK;YAAO,iBAAiB;YAAI,YAAY;QAAG;KACnD;IACD,kBAAkB;QAChB,kBAAkB;QAClB,aAAa;QACb,YAAY;QACZ,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 1484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/%28dashboard%29/symptom_analysis/chronic_condition_snapshot/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { BreadcrumbNavigation } from '@/components/reminders/BreadcrumbNavigation';\nimport { ConditionOverview } from '@/components/chronic-condition/ConditionOverview';\nimport { PlanOverview } from '@/components/chronic-condition/PlanOverview';\nimport { TabNavigation } from '@/components/chronic-condition/TabNavigation';\nimport { ActivityGuidance } from '@/components/chronic-condition/ActivityGuidance';\nimport { EnergyEnvelopeChart } from '@/components/chronic-condition/EnergyEnvelopeChart';\nimport { mockRootProps } from '@/app/chronicConditionSnapshotMockData';\n\nconst breadcrumbItems = [\n    { label: 'User Dashboard', href: '/' },\n    { label: 'Recent Symptom Analysis', href: '/symptom_analysis' },\n    { label: 'Upcoming Reminder', href: '/reminders' },\n    { label: 'Chronic Condition Snapshot' }\n];\n\nexport default function ChronicConditionSnapshotPage() {\n    const { patientData, energyData, activityGuidance } = mockRootProps;\n\n    return (\n        <div className=\"w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background\">\n            <div className=\"w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background mx-auto\">\n                {/* Breadcrumb Navigation */}\n                <BreadcrumbNavigation items={breadcrumbItems} />\n\n                {/* Main Content */}\n                <div className=\"space-y-6\">\n                    {/* Page Title */}\n                    <h1 className=\"health-heading-lg text-[#191919]\">\n                        Chronic Migraine Management Plan\n                    </h1>\n\n                    {/* Main Grid Layout */}\n                    {/* Left Column - Condition Overview */}\n                    <div className=\"lg:col-span-1\">\n                        <ConditionOverview\n                            condition={patientData.condition}\n                            diagnosisDate={patientData.diagnosisDate}\n                            lastReview={patientData.lastReview}\n                            status={patientData.status}\n                        />\n                    </div>\n                    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n\n\n                        {/* Right Column - Plan Details */}\n                        <div className=\"lg:col-span-2 space-y-6\">\n                            {/* Plan Overview */}\n                            <PlanOverview />\n\n                            {/* Tab Navigation */}\n                            <TabNavigation />\n\n                            {/* Activity Guidance */}\n                            <ActivityGuidance\n                                recommendedLevel={activityGuidance.recommendedLevel}\n                                description={activityGuidance.description}\n                                restBreaks={activityGuidance.restBreaks}\n                                restDescription={activityGuidance.restDescription}\n                            />\n\n                            {/* Energy Chart */}\n                            <EnergyEnvelopeChart data={energyData} />\n                        </div>\n                    </div>\n\n                    {/* Share Button */}\n                    <div className=\"flex justify-end\">\n                        <Button className=\"health-button-primary\">\n                            Share Plan with Provider\n                        </Button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAYA,MAAM,kBAAkB;IACpB;QAAE,OAAO;QAAkB,MAAM;IAAI;IACrC;QAAE,OAAO;QAA2B,MAAM;IAAoB;IAC9D;QAAE,OAAO;QAAqB,MAAM;IAAa;IACjD;QAAE,OAAO;IAA6B;CACzC;AAEc,SAAS;IACpB,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,8IAAA,CAAA,gBAAa;IAEnE,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BAEX,8OAAC,uJAAA,CAAA,uBAAoB;oBAAC,OAAO;;;;;;8BAG7B,8OAAC;oBAAI,WAAU;;sCAEX,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,+JAAA,CAAA,oBAAiB;gCACd,WAAW,YAAY,SAAS;gCAChC,eAAe,YAAY,aAAa;gCACxC,YAAY,YAAY,UAAU;gCAClC,QAAQ,YAAY,MAAM;;;;;;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;sCAIX,cAAA,8OAAC;gCAAI,WAAU;;kDAEX,8OAAC,0JAAA,CAAA,eAAY;;;;;kDAGb,8OAAC,2JAAA,CAAA,gBAAa;;;;;kDAGd,8OAAC,8JAAA,CAAA,mBAAgB;wCACb,kBAAkB,iBAAiB,gBAAgB;wCACnD,aAAa,iBAAiB,WAAW;wCACzC,YAAY,iBAAiB,UAAU;wCACvC,iBAAiB,iBAAiB,eAAe;;;;;;kDAIrD,8OAAC,iKAAA,CAAA,sBAAmB;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlE", "debugId": null}}]}