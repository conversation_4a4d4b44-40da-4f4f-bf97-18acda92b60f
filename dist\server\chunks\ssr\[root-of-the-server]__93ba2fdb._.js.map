{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/chat/page.tsx"], "sourcesContent": ["export default function ChatPage() {\r\n  return <div>Chat with Petals Page</div>;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBAAO,8OAAC;kBAAI;;;;;;AACd", "debugId": null}}]}