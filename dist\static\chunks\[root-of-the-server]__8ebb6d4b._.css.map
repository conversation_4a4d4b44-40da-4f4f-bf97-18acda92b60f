{"version": 3, "sources": [], "sections": [{"offset": {"line": 2, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwYGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwSGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwcGFWNOITd.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Fallback';\n    src: local(\"Arial\");\n    ascent-override: 95.94%;\ndescent-override: 28.16%;\nline-gap-override: 0.00%;\nsize-adjust: 104.76%;\n\n}\n.className {\n    font-family: 'Geist', 'Geist Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-sans: 'Geist', 'Geist Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrMdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrkdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrcdmhHkjko.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Mono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 74.67%;\ndescent-override: 21.92%;\nline-gap-override: 0.00%;\nsize-adjust: 134.59%;\n\n}\n.className {\n    font-family: 'Geist Mono', 'Geist Mono Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-mono: 'Geist Mono', 'Geist Mono Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700&display=swap');\n@layer theme {\n  :root, :host {\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-teal-600: oklch(60% 0.118 184.704);\n    --color-teal-700: oklch(51.1% 0.096 186.391);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-700: oklch(49.6% 0.265 301.924);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-sm: 24rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-widest: 0.1em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-xs: 0.125rem;\n    --radius-2xl: 1rem;\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n    --color-border: var(--border);\n    --font-lato: 'Lato', sans-serif;\n  }\n}\n@layer base {\n  *,\n  ::after,\n  ::before,\n  ::backdrop,\n  ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html,\n  :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b,\n  strong {\n    font-weight: bolder;\n  }\n  code,\n  kbd,\n  samp,\n  pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub,\n  sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol,\n  ul,\n  menu {\n    list-style: none;\n  }\n  img,\n  svg,\n  video,\n  canvas,\n  audio,\n  iframe,\n  embed,\n  object {\n    display: block;\n    vertical-align: middle;\n  }\n  img,\n  video {\n    max-width: 100%;\n    height: auto;\n  }\n  button,\n  input,\n  select,\n  optgroup,\n  textarea,\n  ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or\n    (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit,\n  ::-webkit-datetime-edit-year-field,\n  ::-webkit-datetime-edit-month-field,\n  ::-webkit-datetime-edit-day-field,\n  ::-webkit-datetime-edit-hour-field,\n  ::-webkit-datetime-edit-minute-field,\n  ::-webkit-datetime-edit-second-field,\n  ::-webkit-datetime-edit-millisecond-field,\n  ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button,\n  input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]),\n  ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button,\n  ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .-top-1 {\n    top: calc(var(--spacing) * -1);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-3\\.5 {\n    top: calc(var(--spacing) * 3.5);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-3\\.5 {\n    margin-inline: calc(var(--spacing) * 3.5);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-0\\.5 {\n    margin-block: calc(var(--spacing) * 0.5);\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .table-caption {\n    display: table-caption;\n  }\n  .table-cell {\n    display: table-cell;\n  }\n  .table-row {\n    display: table-row;\n  }\n  .field-sizing-content {\n    field-sizing: content;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .aspect-video {\n    aspect-ratio: var(--aspect-video);\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n    height: calc(var(--spacing) * 2.5);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-5 {\n    width: calc(var(--spacing) * 5);\n    height: calc(var(--spacing) * 5);\n  }\n  .size-6 {\n    width: calc(var(--spacing) * 6);\n    height: calc(var(--spacing) * 6);\n  }\n  .size-7 {\n    width: calc(var(--spacing) * 7);\n    height: calc(var(--spacing) * 7);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-2\\.5 {\n    height: calc(var(--spacing) * 2.5);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-\\[400px\\] {\n    height: 400px;\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-svh {\n    height: 100svh;\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-32 {\n    max-height: calc(var(--spacing) * 32);\n  }\n  .max-h-\\[85vh\\] {\n    max-height: 85vh;\n  }\n  .min-h-0 {\n    min-height: calc(var(--spacing) * 0);\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-24 {\n    min-height: calc(var(--spacing) * 24);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .min-h-svh {\n    min-height: 100svh;\n  }\n  .w-\\(--radix-dropdown-menu-trigger-width\\) {\n    width: var(--radix-dropdown-menu-trigger-width);\n  }\n  .w-\\(--sidebar-width\\) {\n    width: var(--sidebar-width);\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-32 {\n    width: calc(var(--spacing) * 32);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-\\[380px\\] {\n    width: 380px;\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-\\(--skeleton-width\\) {\n    max-width: var(--skeleton-width);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-5 {\n    min-width: calc(var(--spacing) * 5);\n  }\n  .min-w-56 {\n    min-width: calc(var(--spacing) * 56);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .caption-bottom {\n    caption-side: bottom;\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .origin-\\(--radix-tooltip-content-transform-origin\\) {\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-px {\n    --tw-translate-x: -1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-px {\n    --tw-translate-x: 1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[calc\\(-50\\%_-_2px\\)\\] {\n    --tw-translate-y: calc(-50% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-rotate-90 {\n    rotate: calc(90deg * -1);\n  }\n  .rotate-45 {\n    rotate: 45deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-in {\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .items-stretch {\n    align-items: stretch;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-5 {\n    gap: calc(var(--spacing) * 5);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .health-table-container {\n    background: white;\n    border-radius: 8px;\n    box-shadow: 6px 6px 54px rgba(0, 0, 0, 0.05);\n    overflow: hidden;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .health-button-primary {\n    background: #7ba4a8;\n    color: white;\n    font-family: var(--font-lato);\n    font-size: 16px;\n    font-weight: 500;\n    border-radius: 8px;\n    padding: 8px 24px;\n  }\n  .health-rounded {\n    border-radius: 14px;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-\\[2px\\] {\n    border-radius: 2px;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius);\n  }\n  .rounded-md {\n    border-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-none {\n    border-radius: 0;\n  }\n  .rounded-sm {\n    border-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-xl {\n    border-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .rounded-b-lg {\n    border-bottom-right-radius: var(--radius);\n    border-bottom-left-radius: var(--radius);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-\\[1\\.5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 1.5px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-l {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-\\(--color-border\\) {\n    border-color: var(--color-border);\n  }\n  .border-\\[\\#eff1f3\\] {\n    border-color: #eff1f3;\n  }\n  .border-border {\n    border-color: var(--border);\n  }\n  .border-border\\/50 {\n    border-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--border) 50%, transparent);\n    }\n  }\n  .border-input {\n    border-color: var(--input);\n  }\n  .border-primary {\n    border-color: var(--primary);\n  }\n  .border-sidebar-border {\n    border-color: var(--sidebar-border);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .bg-\\(--color-bg\\) {\n    background-color: var(--color-bg);\n  }\n  .bg-\\[\\#c8e6f0\\] {\n    background-color: #c8e6f0;\n  }\n  .bg-\\[\\#fff9f9\\] {\n    background-color: #fff9f9;\n  }\n  .bg-accent {\n    background-color: var(--accent);\n  }\n  .bg-background {\n    background-color: var(--background);\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-border {\n    background-color: var(--border);\n  }\n  .bg-card {\n    background-color: var(--card);\n  }\n  .bg-destructive {\n    background-color: var(--destructive);\n  }\n  .bg-gray-50 {\n    background-color: var(--color-gray-50);\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-gray-800 {\n    background-color: var(--color-gray-800);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-200 {\n    background-color: var(--color-green-200);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-green-600 {\n    background-color: var(--color-green-600);\n  }\n  .bg-health-background {\n    background-color: var(--health-background);\n  }\n  .bg-health-chart-teal {\n    background-color: var(--health-chart-teal);\n  }\n  .bg-health-error {\n    background-color: var(--health-error);\n  }\n  .bg-health-light {\n    background-color: var(--health-light);\n  }\n  .bg-indigo-600 {\n    background-color: var(--color-indigo-600);\n  }\n  .bg-muted {\n    background-color: var(--muted);\n  }\n  .bg-muted\\/50 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n  }\n  .bg-orange-600 {\n    background-color: var(--color-orange-600);\n  }\n  .bg-popover {\n    background-color: var(--popover);\n  }\n  .bg-primary {\n    background-color: var(--primary);\n  }\n  .bg-purple-600 {\n    background-color: var(--color-purple-600);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-red-200 {\n    background-color: var(--color-red-200);\n  }\n  .bg-red-500 {\n    background-color: var(--color-red-500);\n  }\n  .bg-red-600 {\n    background-color: var(--color-red-600);\n  }\n  .bg-secondary {\n    background-color: var(--secondary);\n  }\n  .bg-sidebar {\n    background-color: var(--sidebar);\n  }\n  .bg-sidebar-border {\n    background-color: var(--sidebar-border);\n  }\n  .bg-sidebar-primary {\n    background-color: var(--sidebar-primary);\n  }\n  .bg-teal-600 {\n    background-color: var(--color-teal-600);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .fill-primary {\n    fill: var(--primary);\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .px-0 {\n    padding-inline: calc(var(--spacing) * 0);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-10 {\n    padding-inline: calc(var(--spacing) * 10);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .health-body-lg {\n    font-family: var(--font-lato);\n    font-size: 16px;\n    font-weight: 400;\n    color: var(--health-accent);\n  }\n  .health-body-md {\n    font-family: var(--font-lato);\n    font-size: 14px;\n    font-weight: 400;\n    color: var(--health-secondary);\n  }\n  .health-body-sm {\n    font-family: var(--font-lato);\n    font-size: 12px;\n    font-weight: 400;\n    color: var(--health-secondary);\n  }\n  .health-button-text {\n    font-family: var(--font-lato);\n    font-size: 16px;\n    font-weight: 500;\n    color: var(--health-secondary);\n  }\n  .health-caption {\n    font-family: var(--font-lato);\n    font-size: 14px;\n    font-weight: 400;\n    color: var(--health-muted);\n  }\n  .health-heading-lg {\n    font-family: var(--font-lato);\n    font-size: 36px;\n    font-weight: 400;\n    color: var(--health-secondary);\n  }\n  .health-heading-md {\n    font-family: var(--font-lato);\n    font-size: 28px;\n    font-weight: 400;\n    color: var(--health-primary);\n  }\n  .health-heading-sm {\n    font-family: var(--font-lato);\n    font-size: 24px;\n    font-weight: 400;\n    color: var(--health-secondary);\n  }\n  .health-heading-xl {\n    font-family: var(--font-lato);\n    font-size: 48px;\n    font-weight: 400;\n    color: var(--health-primary);\n  }\n  .health-link-text {\n    font-family: var(--font-lato);\n    font-size: 14px;\n    font-weight: 600;\n    color: var(--health-secondary);\n  }\n  .font-lato {\n    font-family: 'Lato', sans-serif;\n  }\n  .font-mono {\n    font-family: var(--font-geist-mono);\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-5xl {\n    font-size: var(--text-5xl);\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[10px\\] {\n    font-size: 10px;\n  }\n  .text-\\[16px\\] {\n    font-size: 16px;\n  }\n  .text-\\[18px\\] {\n    font-size: 18px;\n  }\n  .text-\\[36px\\] {\n    font-size: 36px;\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .text-balance {\n    text-wrap: balance;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-\\[\\#282d32\\] {\n    color: #282d32;\n  }\n  .text-\\[\\#394c6b\\] {\n    color: #394c6b;\n  }\n  .text-\\[\\#191919\\] {\n    color: #191919;\n  }\n  .text-\\[\\#202224\\] {\n    color: #202224;\n  }\n  .text-\\[\\#868686\\] {\n    color: #868686;\n  }\n  .text-\\[rgba\\(43\\,48\\,52\\,0\\.40\\)\\] {\n    color: rgba(43,48,52,0.40);\n  }\n  .text-blue-700 {\n    color: var(--color-blue-700);\n  }\n  .text-blue-800 {\n    color: var(--color-blue-800);\n  }\n  .text-card-foreground {\n    color: var(--card-foreground);\n  }\n  .text-current {\n    color: currentcolor;\n  }\n  .text-foreground {\n    color: var(--foreground);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-green-700 {\n    color: var(--color-green-700);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-health-accent {\n    color: var(--health-accent);\n  }\n  .text-health-muted {\n    color: var(--health-muted);\n  }\n  .text-health-primary {\n    color: var(--health-primary);\n  }\n  .text-health-secondary {\n    color: var(--health-secondary);\n  }\n  .text-indigo-700 {\n    color: var(--color-indigo-700);\n  }\n  .text-muted-foreground {\n    color: var(--muted-foreground);\n  }\n  .text-orange-700 {\n    color: var(--color-orange-700);\n  }\n  .text-popover-foreground {\n    color: var(--popover-foreground);\n  }\n  .text-primary {\n    color: var(--primary);\n  }\n  .text-primary-foreground {\n    color: var(--primary-foreground);\n  }\n  .text-purple-700 {\n    color: var(--color-purple-700);\n  }\n  .text-red-800 {\n    color: var(--color-red-800);\n  }\n  .text-secondary-foreground {\n    color: var(--secondary-foreground);\n  }\n  .text-sidebar-foreground {\n    color: var(--sidebar-foreground);\n  }\n  .text-sidebar-foreground\\/70 {\n    color: var(--sidebar-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\n    }\n  }\n  .text-sidebar-primary-foreground {\n    color: var(--sidebar-primary-foreground);\n  }\n  .text-teal-700 {\n    color: var(--color-teal-700);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .tabular-nums {\n    --tw-numeric-spacing: tabular-nums;\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .health-card-shadow {\n    box-shadow: 6px 6px 54px var(--health-shadow);\n  }\n  .ring-sidebar-ring {\n    --tw-ring-color: var(--sidebar-ring);\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: var(--background);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[left\\,right\\,width\\] {\n    transition-property: left,right,width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[margin\\,opacity\\] {\n    transition-property: margin,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\,padding\\] {\n    transition-property: width,height,padding;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\] {\n    transition-property: width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .ease-linear {\n    --tw-ease: linear;\n    transition-timing-function: linear;\n  }\n  .fade-in-0 {\n    --tw-enter-opacity: calc(0/100);\n    --tw-enter-opacity: 0;\n  }\n  .health-table-header {\n    background: #f5f7fa;\n    border-bottom: 1.1396079063415527px solid #f5f7fa;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .zoom-in-95 {\n    --tw-enter-scale: calc(95*1%);\n    --tw-enter-scale: .95;\n  }\n  .health-table-row {\n    border-bottom: 1.1396079063415527px solid #eff1f3;\n  }\n  .group-focus-within\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):focus-within *) {\n      opacity: 100%;\n    }\n  }\n  .group-hover\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8 {\n    &:is(:where(.group\\/menu-item):has(*[data-sidebar=\"menu-action\"]) *) {\n      padding-right: calc(var(--spacing) * 8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      margin-top: calc(var(--spacing) * -8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      display: none;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:size-8\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--spacing) * 8) !important;\n      height: calc(var(--spacing) * 8) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\) {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: var(--sidebar-width-icon);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-0\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 0) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-2\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 2) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      right: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      left: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:-right-4 {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      right: calc(var(--spacing) * -4);\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:border-r {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:left-0 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:rotate-180 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:border-l {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .group-data-\\[state\\=open\\]\\/collapsible\\:rotate-90 {\n    &:is(:where(.group\\/collapsible)[data-state=\"open\"] *) {\n      rotate: 90deg;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-radius: var(--radius);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-color: var(--sidebar-border);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:shadow-sm {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button):hover ~ *) {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button)[data-active=\"true\"] ~ *) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"default\"] ~ *) {\n      top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"lg\"] ~ *) {\n      top: calc(var(--spacing) * 2.5);\n    }\n  }\n  .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"sm\"] ~ *) {\n      top: calc(var(--spacing) * 1);\n    }\n  }\n  .selection\\:bg-primary {\n    & *::selection {\n      background-color: var(--primary);\n    }\n    &::selection {\n      background-color: var(--primary);\n    }\n  }\n  .selection\\:text-primary-foreground {\n    & *::selection {\n      color: var(--primary-foreground);\n    }\n    &::selection {\n      color: var(--primary-foreground);\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .file\\:text-foreground {\n    &::file-selector-button {\n      color: var(--foreground);\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: var(--muted-foreground);\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:-inset-2 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * -2);\n    }\n  }\n  .after\\:inset-y-0 {\n    &::after {\n      content: var(--tw-content);\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:left-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(1/2 * 100%);\n    }\n  }\n  .after\\:w-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 2px;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      &::after {\n        content: var(--tw-content);\n        left: 100%;\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .hover\\:bg-blue-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-600);\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-900 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-900);\n      }\n    }\n  }\n  .hover\\:bg-green-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-600);\n      }\n    }\n  }\n  .hover\\:bg-green-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-700);\n      }\n    }\n  }\n  .hover\\:bg-health-chart-teal\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--health-chart-teal);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--health-chart-teal) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-indigo-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-indigo-700);\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-orange-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-orange-700);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-purple-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-purple-700);\n      }\n    }\n  }\n  .hover\\:bg-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-600);\n      }\n    }\n  }\n  .hover\\:bg-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-700);\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-sidebar-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar-accent);\n      }\n    }\n  }\n  .hover\\:bg-teal-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-teal-700);\n      }\n    }\n  }\n  .hover\\:bg-white\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--foreground);\n      }\n    }\n  }\n  .hover\\:text-sidebar-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar {\n    &:hover {\n      @media (hover: hover) {\n        &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n          background-color: var(--sidebar);\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-sidebar-border {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--sidebar-border);\n        }\n      }\n    }\n  }\n  .focus\\:bg-accent {\n    &:focus {\n      background-color: var(--accent);\n    }\n  }\n  .focus\\:text-accent-foreground {\n    &:focus {\n      color: var(--accent-foreground);\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-destructive\\/20 {\n    &:focus-visible {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-2 {\n    &:focus-visible {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:bg-sidebar-accent {\n    &:active {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .active\\:text-sidebar-accent-foreground {\n    &:active {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .in-data-\\[side\\=left\\]\\:cursor-w-resize {\n    :where(*[data-side=\"left\"]) & {\n      cursor: w-resize;\n    }\n  }\n  .in-data-\\[side\\=right\\]\\:cursor-e-resize {\n    :where(*[data-side=\"right\"]) & {\n      cursor: e-resize;\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-data-\\[variant\\=inset\\]\\:bg-sidebar {\n    &:has(*[data-variant=\"inset\"]) {\n      background-color: var(--sidebar);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .aria-disabled\\:pointer-events-none {\n    &[aria-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .aria-disabled\\:opacity-50 {\n    &[aria-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-sidebar-accent {\n    &[data-active=\"true\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-medium {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-sidebar-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-px {\n    &[data-orientation=\"horizontal\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:w-full {\n    &[data-orientation=\"horizontal\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-full {\n    &[data-orientation=\"vertical\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-px {\n    &[data-orientation=\"vertical\"] {\n      width: 1px;\n    }\n  }\n  .data-\\[placeholder\\]\\:text-muted-foreground {\n    &[data-placeholder] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {\n    &[data-side=\"bottom\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:slide-in-from-right-2 {\n    &[data-side=\"left\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:slide-in-from-left-2 {\n    &[data-side=\"right\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {\n    &[data-side=\"top\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .data-\\[state\\=active\\]\\:border-b-2 {\n    &[data-state=\"active\"] {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 2px;\n    }\n  }\n  .data-\\[state\\=active\\]\\:border-\\[\\#394c6b\\] {\n    &[data-state=\"active\"] {\n      border-color: #394c6b;\n    }\n  }\n  .data-\\[state\\=active\\]\\:border-health-accent {\n    &[data-state=\"active\"] {\n      border-color: var(--health-accent);\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-background {\n    &[data-state=\"active\"] {\n      background-color: var(--background);\n    }\n  }\n  .data-\\[state\\=active\\]\\:text-\\[\\#394c6b\\] {\n    &[data-state=\"active\"] {\n      color: #394c6b;\n    }\n  }\n  .data-\\[state\\=active\\]\\:text-foreground {\n    &[data-state=\"active\"] {\n      color: var(--foreground);\n    }\n  }\n  .data-\\[state\\=active\\]\\:text-health-accent {\n    &[data-state=\"active\"] {\n      color: var(--health-accent);\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-out {\n    &[data-state=\"closed\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:duration-300 {\n    &[data-state=\"closed\"] {\n      --tw-duration: 300ms;\n      transition-duration: 300ms;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:fade-out-0 {\n    &[data-state=\"closed\"] {\n      --tw-exit-opacity: calc(0/100);\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &[data-state=\"closed\"] {\n      --tw-exit-scale: calc(95*1%);\n      --tw-exit-scale: .95;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-bottom {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-left {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-right {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-top {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: -100%;\n    }\n  }\n  .data-\\[state\\=collapsed\\]\\:w-24 {\n    &[data-state=\"collapsed\"] {\n      width: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[state\\=inactive\\]\\:text-\\[\\#868686\\] {\n    &[data-state=\"inactive\"] {\n      color: #868686;\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-in {\n    &[data-state=\"open\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-secondary {\n    &[data-state=\"open\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:opacity-100 {\n    &[data-state=\"open\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:duration-500 {\n    &[data-state=\"open\"] {\n      --tw-duration: 500ms;\n      transition-duration: 500ms;\n    }\n  }\n  .data-\\[state\\=open\\]\\:fade-in-0 {\n    &[data-state=\"open\"] {\n      --tw-enter-opacity: calc(0/100);\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-95 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: calc(95*1%);\n      --tw-enter-scale: .95;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-bottom {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-left {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-right {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-top {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: -100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--sidebar-accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--sidebar-accent-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=selected\\]\\:bg-muted {\n    &[data-state=\"selected\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        color: var(--destructive);\n      }\n    }\n  }\n  .sm\\:flex {\n    @media (width >= 40rem) {\n      display: flex;\n    }\n  }\n  .sm\\:max-w-sm {\n    @media (width >= 40rem) {\n      max-width: var(--container-sm);\n    }\n  }\n  .sm\\:gap-2\\.5 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:p-10 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 10);\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .md\\:opacity-0 {\n    @media (width >= 48rem) {\n      opacity: 0%;\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin-left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        border-radius: calc(var(--radius) + 4px);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        &:is(:where(.peer)[data-state=\"collapsed\"] ~ *) {\n          margin-left: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .md\\:after\\:hidden {\n    @media (width >= 48rem) {\n      &::after {\n        content: var(--tw-content);\n        display: none;\n      }\n    }\n  }\n  .lg\\:col-span-1 {\n    @media (width >= 64rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .lg\\:col-span-2 {\n    @media (width >= 64rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-6 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .xl\\:grid-cols-4 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .dark\\:border-input {\n    &:is(.dark *) {\n      border-color: var(--input);\n    }\n  }\n  .dark\\:bg-destructive\\/60 {\n    &:is(.dark *) {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:hover\\:bg-accent\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &:focus-visible {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n    &:is(.dark *) {\n      &[data-variant=\"destructive\"] {\n        &:focus {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {\n    & .recharts-cartesian-axis-tick text {\n      fill: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {\n    & .recharts-cartesian-grid line[stroke='#ccc'] {\n      stroke: var(--border);\n      @supports (color: color-mix(in lab, red, red)) {\n        stroke: color-mix(in oklab, var(--border) 50%, transparent);\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {\n    & .recharts-curve.recharts-tooltip-cursor {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-dot[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-layer\\]\\:outline-hidden {\n    & .recharts-layer {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-polar-grid [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {\n    & .recharts-radial-bar-background-sector {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {\n    & .recharts-rectangle.recharts-tooltip-cursor {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-reference-line [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\]\\:outline-hidden {\n    & .recharts-sector {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-sector[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-surface\\]\\:outline-hidden {\n    & .recharts-surface {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n    & svg:not([class*='text-']) {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_tr\\]\\:border-b {\n    & tr {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&_tr\\:last-child\\]\\:border-0 {\n    & tr:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n    &:has([role=checkbox]) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n    &[data-variant=\"destructive\"] {\n      :is(& > *) {\n        &:is(svg) {\n          color: var(--destructive) !important;\n        }\n      }\n    }\n  }\n  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n    &>[role=checkbox] {\n      --tw-translate-y: 2px;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>button\\]\\:hidden {\n    &>button {\n      display: none;\n    }\n  }\n  .\\[\\&\\>span\\:last-child\\]\\:truncate {\n    &>span:last-child {\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:pointer-events-none {\n    &>svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 3.5);\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-4 {\n    &>svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-2\\.5 {\n    &>svg {\n      height: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-3 {\n    &>svg {\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-2\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:shrink-0 {\n    &>svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-muted-foreground {\n    &>svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground {\n    &>svg {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n    &>tr {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n    [data-side=left][data-collapsible=offcanvas] & {\n      right: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n    [data-side=left][data-state=collapsed] & {\n      cursor: e-resize;\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n    [data-side=right][data-collapsible=offcanvas] & {\n      left: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n    [data-side=right][data-state=collapsed] & {\n      cursor: w-resize;\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-accent {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-destructive\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-primary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--primary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-secondary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--secondary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:text-accent-foreground {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--accent-foreground);\n        }\n      }\n    }\n  }\n}\n@property --tw-animation-delay {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0s;\n}\n@property --tw-animation-direction {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: normal;\n}\n@property --tw-animation-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-animation-fill-mode {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: none;\n}\n@property --tw-animation-iteration-count {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-opacity {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-rotate {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-enter-scale {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-enter-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-opacity {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-exit-rotate {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-scale {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-exit-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n:root {\n  --radius: 0.625rem;\n  --background: oklch(1 0 0);\n  --foreground: oklch(0.145 0 0);\n  --card: oklch(1 0 0);\n  --card-foreground: oklch(0.145 0 0);\n  --popover: oklch(1 0 0);\n  --popover-foreground: oklch(0.145 0 0);\n  --primary: oklch(0.205 0 0);\n  --primary-foreground: oklch(0.985 0 0);\n  --secondary: oklch(0.97 0 0);\n  --secondary-foreground: oklch(0.205 0 0);\n  --muted: oklch(0.97 0 0);\n  --muted-foreground: oklch(0.556 0 0);\n  --accent: oklch(0.97 0 0);\n  --accent-foreground: oklch(0.205 0 0);\n  --destructive: oklch(0.577 0.245 27.325);\n  --border: oklch(0.922 0 0);\n  --input: oklch(0.922 0 0);\n  --ring: oklch(0.708 0 0);\n  --chart-1: oklch(0.646 0.222 41.116);\n  --chart-2: oklch(0.6 0.118 184.704);\n  --chart-3: oklch(0.398 0.07 227.392);\n  --chart-4: oklch(0.828 0.189 84.429);\n  --chart-5: oklch(0.769 0.188 70.08);\n  --sidebar: #E6EFFF;\n  --sidebar-foreground: oklch(0.145 0 0);\n  --sidebar-primary: oklch(0.205 0 0);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.97 0 0);\n  --sidebar-accent-foreground: oklch(0.205 0 0);\n  --sidebar-border: oklch(0.922 0 0);\n  --sidebar-ring: oklch(0.708 0 0);\n  --health-primary: #668091;\n  --health-secondary: #41516b;\n  --health-accent: #394c6b;\n  --health-muted: #868686;\n  --health-light: #d1d1d1;\n  --health-background: #fffbfb;\n  --health-success: #10b981;\n  --health-warning: #f59e0b;\n  --health-error: #ef4444;\n  --health-chart-teal: #14b8a6;\n  --health-chart-orange: #f97316;\n  --health-chart-blue: #3b82f6;\n  --health-shadow: rgba(0, 0, 0, 0.05);\n}\n.dark {\n  --background: oklch(0.145 0 0);\n  --foreground: oklch(0.985 0 0);\n  --card: oklch(0.205 0 0);\n  --card-foreground: oklch(0.985 0 0);\n  --popover: oklch(0.205 0 0);\n  --popover-foreground: oklch(0.985 0 0);\n  --primary: oklch(0.922 0 0);\n  --primary-foreground: oklch(0.205 0 0);\n  --secondary: oklch(0.269 0 0);\n  --secondary-foreground: oklch(0.985 0 0);\n  --muted: oklch(0.269 0 0);\n  --muted-foreground: oklch(0.708 0 0);\n  --accent: oklch(0.269 0 0);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.704 0.191 22.216);\n  --border: oklch(1 0 0 / 10%);\n  --input: oklch(1 0 0 / 15%);\n  --ring: oklch(0.556 0 0);\n  --chart-1: oklch(0.488 0.243 264.376);\n  --chart-2: oklch(0.696 0.17 162.48);\n  --chart-3: oklch(0.769 0.188 70.08);\n  --chart-4: oklch(0.627 0.265 303.9);\n  --chart-5: oklch(0.645 0.246 16.439);\n  --sidebar: oklch(0.205 0 0);\n  --sidebar-foreground: oklch(0.985 0 0);\n  --sidebar-primary: oklch(0.488 0.243 264.376);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.269 0 0);\n  --sidebar-accent-foreground: oklch(0.985 0 0);\n  --sidebar-border: oklch(1 0 0 / 10%);\n  --sidebar-ring: oklch(0.556 0 0);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ordinal {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-slashed-zero {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-figure {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-spacing {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-fraction {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes enter {\n  from {\n    opacity: var(--tw-enter-opacity,1);\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\n  }\n}\n@keyframes exit {\n  to {\n    opacity: var(--tw-exit-opacity,1);\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-ordinal: initial;\n      --tw-slashed-zero: initial;\n      --tw-numeric-figure: initial;\n      --tw-numeric-spacing: initial;\n      --tw-numeric-fraction: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n      --tw-animation-delay: 0s;\n      --tw-animation-direction: normal;\n      --tw-animation-duration: initial;\n      --tw-animation-fill-mode: none;\n      --tw-animation-iteration-count: 1;\n      --tw-enter-opacity: 1;\n      --tw-enter-rotate: 0;\n      --tw-enter-scale: 1;\n      --tw-enter-translate-x: 0;\n      --tw-enter-translate-y: 0;\n      --tw-exit-opacity: 1;\n      --tw-exit-rotate: 0;\n      --tw-exit-scale: 1;\n      --tw-exit-translate-x: 0;\n      --tw-exit-translate-y: 0;\n    }\n  }\n}"], "names": [], "mappings": "AACA;;AAGA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EF;EACE;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;;EAKA;;;;;EAIA;;;;;EASA;;;;;;;EAKA;;;;EAIA;;;;;;;EASA;;;;EAGA;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAKA;;;;;EAWA;;;;;EAKA;;;;;;;;;;;EAAA;;;;;;;;;;;EAAA;;;;;;;;;;;EAeA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;IAEE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAWA;;;;EAGA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAKA;;;;EAAA;;;;EAIA;;;;EA+9FA;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;;AAl+FF;EACE;;;;EAIA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAMF;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;EASA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAIA;;;;EAIA;;;;EAIE;;;;EAME;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAOE;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAGA;;;;EAKA;;;;EAGA;;;;EAKA;;;;EAAA;;;;EAKA;;;;EAAA;;;;EAKA;;;;;EAAA;;;;;EAMA;;;;EAAA;;;;EAKA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAMA;;;;EAAA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAOE;;;;;EAQA;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IACE;;;;;EAQF;IACE;;;;;;EAQJ;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAMF;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;EASvB;IAAuB;;;;;EAO3B;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;;EAOF;IAEI;;;;;EAOJ;IACE;;;;;;EAOF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAQ9C;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EASpD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EAQtD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAOI;;;;EAAA;;;;EAAA;;;;EASA;;;;EAAA;;;;EAAA;;;;EASA;;;;EAAA;;;;EAAA;;;;EASA;;;;EAAA;;;;EAAA;;;;EAOJ;;;;;EAMA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;EASvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;;AAO/B;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;EACE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA", "debugId": null}}]}