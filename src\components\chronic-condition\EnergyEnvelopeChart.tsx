import React from 'react';
import { AreaChart, Area, XAxis, YA<PERSON><PERSON>, ResponsiveContainer, Legend } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Card, CardContent } from '@/components/ui/card';
import OrangeDotIcon from '@/components/icons/OrangeDotIcon';
import PurpleDotIcon from '@/components/icons/PurpleDotIcon';

interface EnergyData {
  day: string;
  allocatedEnergy: number;
  usedEnergy: number;
}

interface EnergyEnvelopeChartProps {
  data: EnergyData[];
}

const chartConfig = {
  allocatedEnergy: {
    label: "Allocated Energy",
    color: "#f9978a",
  },
  usedEnergy: {
    label: "Used Energy", 
    color: "#e3b9ff",
  },
};

export function EnergyEnvelopeChart({ data }: EnergyEnvelopeChartProps) {
  return (
    <div className="space-y-4">
      <h2 className="health-heading-sm">Energy Envelope - This Week</h2>
      
      <Card className="health-card-shadow health-rounded bg-white p-6">
        <CardContent className="p-0">
          <ChartContainer config={chartConfig} className="h-[400px] w-full">
            <AreaChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <XAxis 
                dataKey="day" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#868686' }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#868686' }}
                domain={[0, 100]}
              />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Area
                type="monotone"
                dataKey="allocatedEnergy"
                stackId="1"
                stroke="#f9978a"
                fill="#f9978a"
                fillOpacity={0.8}
              />
              <Area
                type="monotone"
                dataKey="usedEnergy"
                stackId="1"
                stroke="#e3b9ff"
                fill="#e3b9ff"
                fillOpacity={0.8}
              />
            </AreaChart>
          </ChartContainer>
          
          {/* Custom Legend */}
          <div className="flex items-center justify-center gap-6 mt-4">
            <div className="flex items-center gap-2">
              <OrangeDotIcon width={13} height={12} />
              <span className="health-body-lg text-[#282d32] font-bold">Allocated Energy</span>
            </div>
            <div className="flex items-center gap-2">
              <PurpleDotIcon width={13} height={12} />
              <span className="health-body-lg text-[#282d32] font-bold">Used Energy</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}