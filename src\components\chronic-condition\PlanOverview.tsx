import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

const keyStrategies = [
  "Pacing & Energy Management - Optimize daily activities to prevent overexertion",
  "Medication Adherence - Consistent preventive treatment with rescue options", 
  "Trigger Identification - Systematic tracking and avoidance of personal triggers",
  "Progress Monitoring - Regular assessment of symptoms and plan effectiveness"
];

export function PlanOverview() {
  return (
    <div className="space-y-4">
      <h2 className="health-heading-sm">Your Plan Overview</h2>
      
      <Card className="health-card-shadow health-rounded bg-white p-6">
        <CardContent className="p-0 space-y-6">
          <p className="health-body-lg text-health-accent leading-relaxed">
            Your Personalized migraine management plan focuses on proactive prevention through 
            lifestyle optimization, trigger avoidance, and strategic medication use. The plan 
            emphasizes sustainable pacing strategies to maintain energy levels while reducing 
            migraine frequency and severity
          </p>
          
          <div className="space-y-4">
            <h3 className="health-body-lg text-health-accent font-medium">Key Strategies</h3>
            <div className="space-y-2">
              {keyStrategies.map((strategy, index) => (
                <p key={index} className="health-caption text-health-muted">
                  {strategy}
                </p>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}