import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { ActionButtons } from './ActionButtons';
import { formatDate, formatFrequency } from '@/lib/formatters';
import { MedicationFrequency } from '@/mocks/enums';

interface Medication {
  id: string;
  name: string;
  dosage: string;
  time: Date;
  frequency: MedicationFrequency;
}

interface MedicationsTableProps {
  medications: Medication[];
  onAction: (medicationId: string, action: string) => void;
}

export function MedicationsTable({ medications, onAction }: MedicationsTableProps) {
  return (
    <div className="health-table-container">
      <Table>
        <TableHeader>
          <TableRow className="health-table-header">
            <TableHead className="text-[18px] font-normal font-lato text-[#191919] px-10">Medication Name</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Dosage</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Time</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Frequency</TableHead>
            <TableHead className="text-[18px] font-normal font-lato text-[#191919]">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {medications.map((medication) => (
            <TableRow key={medication.id} className="health-table-row">
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b] px-10">{medication.name}</TableCell>
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b]">{medication.dosage}</TableCell>
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b]">
                {medication.time.toLocaleTimeString('en-US', { 
                  hour: 'numeric', 
                  minute: '2-digit', 
                  hour12: true 
                })}
              </TableCell>
              <TableCell className="text-[16px] font-medium font-lato text-[#394c6b]">
                {formatFrequency(medication.frequency)}
              </TableCell>
              <TableCell>
                <ActionButtons
                  itemId={medication.id}
                  type="medication"
                  onAction={onAction}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}