'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { BreadcrumbNavigation } from '@/components/reminders/BreadcrumbNavigation';
import { ConditionOverview } from '@/components/chronic-condition/ConditionOverview';
import { PlanOverview } from '@/components/chronic-condition/PlanOverview';
import { TabNavigation } from '@/components/chronic-condition/TabNavigation';
import { ActivityGuidance } from '@/components/chronic-condition/ActivityGuidance';
import { EnergyEnvelopeChart } from '@/components/chronic-condition/EnergyEnvelopeChart';
import { mockRootProps } from '@/mocks/chronicConditionSnapshotMockData';

const breadcrumbItems = [
    { label: 'User Dashboard', href: '/' },
    { label: 'Recent Symptom Analysis', href: '/symptom_analysis' },
    { label: 'Upcoming Reminder', href: '/reminders' },
    { label: 'Chronic Condition Snapshot' }
];

export default function ChronicConditionSnapshotPage() {
    const { patientData, energyData, activityGuidance } = mockRootProps;

    return (
        <div className="w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background">
            <div className="w-full min-h-screen sm:p-10 space-y-6 bg-health-background mx-auto">
                {/* Breadcrumb Navigation */}
                <BreadcrumbNavigation items={breadcrumbItems} />

                {/* Main Content */}
                <div className="space-y-6">
                    {/* Page Title */}
                    <h1 className="health-heading-lg text-[#191919]">
                        Chronic Migraine Management Plan
                    </h1>

                    {/* Main Grid Layout */}
                    {/* Left Column - Condition Overview */}
                    <div className="lg:col-span-1">
                        <ConditionOverview
                            condition={patientData.condition}
                            diagnosisDate={patientData.diagnosisDate}
                            lastReview={patientData.lastReview}
                            status={patientData.status}
                        />
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">


                        {/* Right Column - Plan Details */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Plan Overview */}
                            <PlanOverview />

                            {/* Tab Navigation */}
                            <TabNavigation />

                            {/* Activity Guidance */}
                            <ActivityGuidance
                                recommendedLevel={activityGuidance.recommendedLevel}
                                description={activityGuidance.description}
                                restBreaks={activityGuidance.restBreaks}
                                restDescription={activityGuidance.restDescription}
                            />

                            {/* Energy Chart */}
                            <EnergyEnvelopeChart data={energyData} />
                        </div>
                    </div>

                    {/* Share Button */}
                    <div className="flex justify-end">
                        <Button className="health-button-primary">
                            Share Plan with Provider
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}