{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/PersonalizedHeader.tsx"], "sourcesContent": ["\"use client\";\n\ninterface PersonalizedHeaderProps {\n  userName: string;\n  currentDate: Date;\n}\n\nexport function PersonalizedHeader({ userName, currentDate }: PersonalizedHeaderProps) {\n  const formatDateTime = (date: Date): string => {\n    return date.toLocaleDateString('en-US', { \n      weekday: 'long', \n      month: 'long', \n      day: 'numeric' \n    }) + ' - ' + date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit',\n      hour12: true \n    });\n  };\n\n  return (\n    <header className=\"flex items-center justify-between mb-6\">\n      <div className=\"space-y-1\">\n        <h1 className=\"text-5xl font-normal\" style={{ color: 'var(--health-secondary)' }}>\n          Hello, {userName}\n        </h1>\n        <p className=\"text-sm\" style={{ color: 'var(--health-muted)' }}>\n          Every step towards wellness counts. You've got this\n        </p>\n      </div>\n      <div className=\"health-caption\">\n        {formatDateTime(currentDate)}\n      </div>\n    </header>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;;AAOO,SAAS,mBAAmB,KAAkD;QAAlD,EAAE,QAAQ,EAAE,WAAW,EAA2B,GAAlD;IACjC,MAAM,iBAAiB,CAAC;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,OAAO;YACP,KAAK;QACP,KAAK,QAAQ,KAAK,kBAAkB,CAAC,SAAS;YAC5C,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;wBAAuB,OAAO;4BAAE,OAAO;wBAA0B;;4BAAG;4BACxE;;;;;;;kCAEV,6LAAC;wBAAE,WAAU;wBAAU,OAAO;4BAAE,OAAO;wBAAsB;kCAAG;;;;;;;;;;;;0BAIlE,6LAAC;gBAAI,WAAU;0BACZ,eAAe;;;;;;;;;;;;AAIxB;KA5BgB", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ChartLineIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 22.286 22.286\" {...props}><g xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#455F84\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2}><path d=\"M0 0v22.286h22.286\" /><path d=\"m5.143 10.285 4.285 4.286 6.857-10.286 6 4.286\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAqB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAE,OAAM;YAA6B,QAAO;YAAU,eAAc;YAAQ,gBAAe;YAAQ,aAAa;;8BAAG,6LAAC;oBAAK,GAAE;;;;;;8BAAuB,6LAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;KAAtS;uCACS", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ClockIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 20 20\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" d=\"M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16m0-18a10.008 10.008 0 0 1 9.239 6.173A10.001 10.001 0 0 1 10 20C4.47 20 0 15.5 0 10A9.997 9.997 0 0 1 10 0m.5 5v5.25l4.5 2.67-.75 1.23L9 11V5z\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,GAAE;;;;;;;;;;;KAAlM;uCACS", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/TriangleAlertIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 21.052 18.5\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" stroke=\"currentColor\" d=\"M10.527.5a.492.492 0 0 1 .432.25l9.527 16.5a.5.5 0 0 1 0 .5.5.5 0 0 1-.434.25H1a.5.5 0 0 1-.432-.75L10.093.75a.5.5 0 0 1 .434-.25zm-.434 2.25-7.794 13.5-.432.75h17.319l-.433-.75-7.794-13.5-.432-.75zM11.027 14v1h-1v-1zm0-7v4h-1V7z\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAmB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,QAAO;YAAe,GAAE;;;;;;;;;;;KAA9N;uCACS", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/HeartIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 22 19.163\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#455F84\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6.504 1c-3.04 0-5.5 2.463-5.5 5.5 0 5.5 6.5 10.5 10 11.663 3.5-1.163 10-6.163 10-11.663 0-3.037-2.47-5.5-5.5-5.5-1.86 0-3.51.924-4.5 2.337a5.6 5.6 0 0 0-1.97-1.719A5.5 5.5 0 0 0 6.504 1\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAiB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,QAAO;YAAU,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;KAAhQ;uCACS", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/HealthMetricCard.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport ChartLineIcon from \"@/components/icons/ChartLineIcon\";\nimport ClockIcon from \"@/components/icons/ClockIcon\";\nimport TriangleAlertIcon from \"@/components/icons/TriangleAlertIcon\";\nimport HeartIcon from \"@/components/icons/HeartIcon\";\n\ninterface HealthMetricCardProps {\n  id: string;\n  title: string;\n  icon: string;\n  details: Record<string, any>;\n  action: string;\n}\n\nexport function HealthMetricCard({ id, title, icon, details, action }: HealthMetricCardProps) {\n  const renderIcon = () => {\n    const iconProps = { width: 22, height: 22, color: \"#455f84\" };\n    \n    switch (icon) {\n      case \"chart-line\":\n        return <ChartLineIcon {...iconProps} />;\n      case \"clock\":\n        return <ClockIcon {...iconProps} />;\n      case \"triangle-alert\":\n        return <TriangleAlertIcon {...iconProps} />;\n      case \"heart\":\n        return <HeartIcon {...iconProps} />;\n      default:\n        return null;\n    }\n  };\n\n  const renderDetails = () => {\n    switch (id) {\n      case \"recent-symptom\":\n        return (\n          <div className=\"space-y-1\">\n            <p className=\"health-body-sm\">Symptom: {details.symptom}</p>\n            <p className=\"health-body-sm\">Severity: {details.severity}</p>\n            <p className=\"health-body-sm\">\n              Detected: {details.detectedDate?.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}\n            </p>\n          </div>\n        );\n      case \"upcoming-reminders\":\n        return (\n          <div className=\"space-y-1\">\n            <p className=\"health-body-sm\">Next Medication: {details.nextMedication}</p>\n            <p className=\"health-body-sm\">\n              Time: {details.time?.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}\n            </p>\n          </div>\n        );\n      case \"chronic-condition\":\n        return (\n          <div className=\"space-y-1\">\n            <p className=\"health-body-sm\">Condition: {details.condition}</p>\n            <p className=\"health-body-sm\">\n              Next Review: {details.nextReview?.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}\n            </p>\n          </div>\n        );\n      case \"key-vitals\":\n        return (\n          <div className=\"space-y-1\">\n            <p className=\"health-body-sm\">Heart Rate: {details.heartRate} BPM</p>\n            <p className=\"health-body-sm\">Sleep: {details.sleepHours} hrs</p>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Card className=\"health-card-shadow health-rounded\">\n      <CardHeader className=\"pb-4\">\n        <div className=\"flex items-start gap-3\">\n          {renderIcon()}\n          <div className=\"flex-1\">\n            <CardTitle className=\"health-body-lg mb-2\">{title}</CardTitle>\n            {renderDetails()}\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <Button variant=\"link\" className=\"px-0 health-link-text\">\n          {action}\n        </Button>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAiBO,SAAS,iBAAiB,KAA2D;QAA3D,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAyB,GAA3D;IAC/B,MAAM,aAAa;QACjB,MAAM,YAAY;YAAE,OAAO;YAAI,QAAQ;YAAI,OAAO;QAAU;QAE5D,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,+IAAA,CAAA,UAAa;oBAAE,GAAG,SAAS;;;;;;YACrC,KAAK;gBACH,qBAAO,6LAAC,2IAAA,CAAA,UAAS;oBAAE,GAAG,SAAS;;;;;;YACjC,KAAK;gBACH,qBAAO,6LAAC,mJAAA,CAAA,UAAiB;oBAAE,GAAG,SAAS;;;;;;YACzC,KAAK;g<PERSON><PERSON>,qBAAO,6LAAC,2IAAA,CAAA,UAAS;oBAAE,GAAG,SAAS;;;;;;YACjC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;oBAMc;gBALjB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAiB;gCAAU,QAAQ,OAAO;;;;;;;sCACvD,6LAAC;4BAAE,WAAU;;gCAAiB;gCAAW,QAAQ,QAAQ;;;;;;;sCACzD,6LAAC;4BAAE,WAAU;;gCAAiB;iCACjB,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,kBAAkB,CAAC,SAAS;oCAAE,OAAO;oCAAQ,KAAK;gCAAU;;;;;;;;;;;;;YAIrG,KAAK;oBAKU;gBAJb,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAiB;gCAAkB,QAAQ,cAAc;;;;;;;sCACtE,6LAAC;4BAAE,WAAU;;gCAAiB;iCACrB,gBAAA,QAAQ,IAAI,cAAZ,oCAAA,cAAc,kBAAkB,CAAC,SAAS;oCAAE,MAAM;oCAAW,QAAQ;oCAAW,QAAQ;gCAAK;;;;;;;;;;;;;YAI5G,KAAK;oBAKiB;gBAJpB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAiB;gCAAY,QAAQ,SAAS;;;;;;;sCAC3D,6LAAC;4BAAE,WAAU;;gCAAiB;iCACd,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,oBAAoB,kBAAkB,CAAC,SAAS;oCAAE,OAAO;oCAAQ,KAAK;gCAAU;;;;;;;;;;;;;YAItG,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAiB;gCAAa,QAAQ,SAAS;gCAAC;;;;;;;sCAC7D,6LAAC;4BAAE,WAAU;;gCAAiB;gCAAQ,QAAQ,UAAU;gCAAC;;;;;;;;;;;;;YAG/D;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuB;;;;;;gCAC3C;;;;;;;;;;;;;;;;;;0BAIP,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAO,WAAU;8BAC9B;;;;;;;;;;;;;;;;;AAKX;KA9EgB", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/HealthMetricsGrid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { HealthMetricCard } from \"./HealthMetricCard\";\n\ninterface HealthMetric {\n  id: string;\n  title: string;\n  icon: string;\n  details: Record<string, any>;\n  action: string;\n}\n\ninterface HealthMetricsGridProps {\n  metrics: HealthMetric[];\n}\n\nexport function HealthMetricsGrid({ metrics }: HealthMetricsGridProps) {\n  return (\n    <section className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-5 mb-6\">\n      {metrics.map((metric) => (\n        <HealthMetricCard key={metric.id} {...metric} />\n      ))}\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBO,SAAS,kBAAkB,KAAmC;QAAnC,EAAE,OAAO,EAA0B,GAAnC;IAChC,qBACE,6LAAC;QAAQ,WAAU;kBAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,sJAAA,CAAA,mBAAgB;gBAAkB,GAAG,MAAM;eAArB,OAAO,EAAE;;;;;;;;;;AAIxC;KARgB", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/HappyFaceIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 20 20\" {...props}><g xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#455F84\" strokeLinecap=\"round\" strokeLinejoin=\"round\"><path d=\"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10\" /><path d=\"M6 13a5 5 0 0 0 6.236 1.472A5 5 0 0 0 14 13M6.009 7H6m8 0h-.009\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAE,OAAM;YAA6B,QAAO;YAAU,eAAc;YAAQ,gBAAe;;8BAAQ,6LAAC;oBAAK,GAAE;;;;;;8BAA4E,6LAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;KAAnU;uCACS", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/NeutralFaceIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 20 20\" {...props}><g xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#455F84\" strokeLinejoin=\"round\"><path d=\"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10z\" /><path strokeLinecap=\"round\" d=\"M13.498 7v.5m-7-.5v.5m7 6s-1-2-3.5-2-3.5 2-3.5 2\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAE,OAAM;YAA6B,QAAO;YAAU,gBAAe;;8BAAQ,6LAAC;oBAAK,GAAE;;;;;;8BAA6E,6LAAC;oBAAK,eAAc;oBAAQ,GAAE;;;;;;;;;;;;;;;;;KAApU;uCACS", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/SadFaceIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" {...props}><defs><clipPath xmlns=\"http://www.w3.org/2000/svg\" id=\"prefix__a\"><path fill=\"#fff\" d=\"M0 0h24v24H0z\" /></clipPath></defs><g xmlns=\"http://www.w3.org/2000/svg\" fill=\"#1F2F4C\" clipPath=\"url(#prefix__a)\"><path stroke=\"#455F84\" strokeWidth={0.2} d=\"M20.795.681v.001l.***************.081.09a12.44 12.44 0 0 1 1.165 1.559c.636 1.008 1.279 2.37 1.28 3.757 0 .859-.266 1.528-.68 2.008.439 1.209.68 2.513.68 3.873 0 6.27-5.08 11.35-11.35 11.35S.65 18.27.65 12 5.73.65 12 .65l.48.009a11.3 11.3 0 0 1 6.495 2.391A12 12 0 0 1 20.482.866l.122-.137.034-.036.008-.008.002-.003h.001l.073-.077zM12 1.787C6.369 1.787 1.787 6.369 1.787 12S6.369 22.213 12 22.213 22.213 17.631 22.213 12c0-1.081-.174-2.154-.512-3.181a2.65 2.65 0 0 1-2.335-.183c-.738-.447-1.273-1.291-1.273-2.516 0-.686.16-1.365.395-1.998A10.16 10.16 0 0 0 12 1.787z\" /><path d=\"M16.312 15.375a1.875 1.875 0 1 0 .001-3.75 1.875 1.875 0 0 0-.001 3.75M7.688 15.375a1.874 1.874 0 1 0 0-3.748 1.874 1.874 0 0 0 0 3.748M10.121 8.84c.203-.176-.364-.773-.53-.627a5.34 5.34 0 0 1-4.385 1.174c-.217-.042-.408.758-.144.809a6.16 6.16 0 0 0 5.059-1.356m8.671.49a5.34 5.34 0 0 1-4.385-1.174c-.166-.146-.733.451-.529.627a6.16 6.16 0 0 0 5.059 1.356c.264-.051.072-.851-.145-.809M15 17.998H9c-.564 0-.564 1.5 0 1.5h6c.564 0 .564-1.5 0-1.5\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAa,GAAG,KAAK;;0BAAE,6LAAC;0BAAK,cAAA,6LAAC;oBAAS,OAAM;oBAA6B,IAAG;8BAAY,cAAA,6LAAC;wBAAK,MAAK;wBAAO,GAAE;;;;;;;;;;;;;;;;0BAAoC,6LAAC;gBAAE,OAAM;gBAA6B,MAAK;gBAAU,UAAS;;kCAAkB,6LAAC;wBAAK,QAAO;wBAAU,aAAa;wBAAK,GAAE;;;;;;kCAA4jB,6LAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;;;KAA57B;uCACS", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/MoodSelector.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport HappyFaceIcon from \"@/components/icons/HappyFaceIcon\";\nimport NeutralFaceIcon from \"@/components/icons/NeutralFaceIcon\";\nimport SadFaceIcon from \"@/components/icons/SadFaceIcon\";\n\ntype MoodType = \"happy\" | \"neutral\" | \"sad\" | null;\n\ninterface MoodSelectorProps {\n  onMoodChange?: (mood: MoodType) => void;\n}\n\nexport function MoodSelector({ onMoodChange }: MoodSelectorProps) {\n  const [selectedMood, setSelectedMood] = useState<MoodType>(null);\n\n  const handleMoodSelect = (mood: MoodType) => {\n    setSelectedMood(mood);\n    onMoodChange?.(mood);\n  };\n\n  return (\n    <div className=\"space-y-2\">\n      <p className=\"health-heading-md\">Mood:</p>\n      <div className=\"flex gap-2\">\n        <Button\n          variant=\"outline\"\n          size=\"icon\"\n          aria-label=\"happy\"\n          onClick={() => handleMoodSelect(\"happy\")}\n          className={selectedMood === \"happy\" ? \"bg-health-background\" : \"\"}\n        >\n          <HappyFaceIcon width={20} height={20} color=\"#41516b\" />\n        </Button>\n        <Button\n          variant=\"outline\"\n          size=\"icon\"\n          aria-label=\"neutral\"\n          onClick={() => handleMoodSelect(\"neutral\")}\n          className={selectedMood === \"neutral\" ? \"bg-health-background\" : \"\"}\n        >\n          <NeutralFaceIcon width={20} height={20} color=\"#41516b\" />\n        </Button>\n        <Button\n          variant=\"outline\"\n          size=\"icon\"\n          aria-label=\"sad\"\n          onClick={() => handleMoodSelect(\"sad\")}\n          className={selectedMood === \"sad\" ? \"bg-health-background\" : \"\"}\n        >\n          <SadFaceIcon width={24} height={24} color=\"#41516b\" />\n        </Button>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAcO,SAAS,aAAa,KAAmC;QAAnC,EAAE,YAAY,EAAqB,GAAnC;;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAE3D,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,yBAAA,mCAAA,aAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAoB;;;;;;0BACjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,cAAW;wBACX,SAAS,IAAM,iBAAiB;wBAChC,WAAW,iBAAiB,UAAU,yBAAyB;kCAE/D,cAAA,6LAAC,+IAAA,CAAA,UAAa;4BAAC,OAAO;4BAAI,QAAQ;4BAAI,OAAM;;;;;;;;;;;kCAE9C,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,cAAW;wBACX,SAAS,IAAM,iBAAiB;wBAChC,WAAW,iBAAiB,YAAY,yBAAyB;kCAEjE,cAAA,6LAAC,iJAAA,CAAA,UAAe;4BAAC,OAAO;4BAAI,QAAQ;4BAAI,OAAM;;;;;;;;;;;kCAEhD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,cAAW;wBACX,SAAS,IAAM,iBAAiB;wBAChC,WAAW,iBAAiB,QAAQ,yBAAyB;kCAE7D,cAAA,6LAAC,6IAAA,CAAA,UAAW;4BAAC,OAAO;4BAAI,QAAQ;4BAAI,OAAM;;;;;;;;;;;;;;;;;;;;;;;AAKpD;GA1CgB;KAAA", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/SymptomLogger.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface SymptomLoggerProps {\n  onSubmit?: (symptom: string) => void;\n}\n\nexport function SymptomLogger({ onSubmit }: SymptomLoggerProps) {\n  const [symptom, setSymptom] = useState(\"\");\n\n  const handleSubmit = () => {\n    if (symptom.trim()) {\n      onSubmit?.(symptom);\n      setSymptom(\"\");\n    }\n  };\n\n  return (\n    <div className=\"space-y-2\">\n      <p className=\"health-heading-md\">Symptom Description:</p>\n      <Textarea\n        placeholder=\"Enter symptom.....\"\n        value={symptom}\n        onChange={(e) => setSymptom(e.target.value)}\n        className=\"min-h-24 resize-none\"\n      />\n      <div className=\"flex justify-end\">\n        <Button \n          onClick={handleSubmit}\n          className=\"bg-health-chart-teal hover:bg-health-chart-teal/90 text-white font-bold px-6 py-2\"\n          style={{ backgroundColor: 'var(--health-chart-teal)' }}\n        >\n          Submit Log\n        </Button>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,cAAc,KAAgC;QAAhC,EAAE,QAAQ,EAAsB,GAAhC;;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,IAAI,QAAQ,IAAI,IAAI;YAClB,qBAAA,+BAAA,SAAW;YACX,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAoB;;;;;;0BACjC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,aAAY;gBACZ,OAAO;gBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gBAC1C,WAAU;;;;;;0BAEZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,WAAU;oBACV,OAAO;wBAAE,iBAAiB;oBAA2B;8BACtD;;;;;;;;;;;;;;;;;AAMT;GA9BgB;KAAA", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/QuickMoodSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { MoodSelector } from \"./MoodSelector\";\nimport { SymptomLogger } from \"./SymptomLogger\";\n\nexport function QuickMoodSection() {\n  const handleMoodChange = (mood: string | null) => {\n    console.log(\"Mood selected:\", mood);\n  };\n\n  const handleSymptomSubmit = (symptom: string) => {\n    console.log(\"Symptom submitted:\", symptom);\n  };\n\n  return (\n    <section className=\"space-y-3 mb-6\">\n      <h2 className=\"text-4xl font-normal text-health-secondary mb-3\">Quick Mood</h2>\n      <Card className=\"shadow-lg rounded-2xl\">\n        <CardContent className=\"p-6 space-y-6\">\n          <MoodSelector onMoodChange={handleMoodChange} />\n          <SymptomLogger onSubmit={handleSymptomSubmit} />\n        </CardContent>\n      </Card>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAG,WAAU;0BAAkD;;;;;;0BAChE,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,kJAAA,CAAA,eAAY;4BAAC,cAAc;;;;;;sCAC5B,6LAAC,mJAAA,CAAA,gBAAa;4BAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAKnC;KApBgB", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/QuickActionsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui/button\";\n\ninterface QuickAction {\n  id: string;\n  label: string;\n  type: string;\n}\n\ninterface QuickActionsSectionProps {\n  actions: QuickAction[];\n}\n\nexport function QuickActionsSection({ actions }: QuickActionsSectionProps) {\n  const handleActionClick = (action: QuickAction) => {\n    console.log(\"Action clicked:\", action);\n  };\n\n  return (\n    <section className=\"space-y-3 mb-6\">\n      <h2 className=\"health-heading-lg\">Quick Actions</h2>\n      <div className=\"flex flex-wrap gap-5\">\n        {actions.map((action) => (\n          <Button\n            key={action.id}\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleActionClick(action)}\n            className=\"health-button-text\"\n          >\n            {action.label}\n          </Button>\n        ))}\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcO,SAAS,oBAAoB,KAAqC;QAArC,EAAE,OAAO,EAA4B,GAArC;IAClC,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAClC,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,kBAAkB;wBACjC,WAAU;kCAET,OAAO,KAAK;uBANR,OAAO,EAAE;;;;;;;;;;;;;;;;AAY1B;KAvBgB", "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/ActivityTimeline.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\n\ninterface RecentActivity {\n  id: string;\n  text: string;\n  action: string;\n  date: Date;\n}\n\ninterface ActivityTimelineProps {\n  activities: RecentActivity[];\n}\n\nexport function ActivityTimeline({ activities }: ActivityTimelineProps) {\n  const handleActionClick = (activity: RecentActivity) => {\n    console.log(\"Activity action clicked:\", activity);\n  };\n\n  return (\n    <section className=\"space-y-3 mb-6\">\n      <h2 className=\"health-heading-lg\">Recent Activity & Insights</h2>\n      <div className=\"space-y-2\">\n        {activities.map((activity) => (\n          <Card key={activity.id}>\n            <CardContent className=\"p-3 flex items-center justify-between gap-4\">\n              <span className=\"health-body-lg\">{activity.text}</span>\n              <Button \n                variant=\"link\" \n                className=\"px-0 health-link-text\"\n                onClick={() => handleActionClick(activity)}\n              >\n                {activity.action}\n              </Button>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBO,SAAS,iBAAiB,KAAqC;QAArC,EAAE,UAAU,EAAyB,GAArC;IAC/B,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,4BAA4B;IAC1C;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAClC,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAK,WAAU;8CAAkB,SAAS,IAAI;;;;;;8CAC/C,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB;8CAEhC,SAAS,MAAM;;;;;;;;;;;;uBARX,SAAS,EAAE;;;;;;;;;;;;;;;;AAgBhC;KA1BgB", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [k in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction ChartContainer({\r\n  id,\r\n  className,\r\n  children,\r\n  config,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  config: ChartConfig\r\n  children: React.ComponentProps<\r\n    typeof RechartsPrimitive.ResponsiveContainer\r\n  >[\"children\"]\r\n}) {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-slot=\"chart\"\r\n        data-chart={chartId}\r\n        className={cn(\r\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n}\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nfunction ChartTooltipContent({\r\n  active,\r\n  payload,\r\n  className,\r\n  indicator = \"dot\",\r\n  hideLabel = false,\r\n  hideIndicator = false,\r\n  label,\r\n  labelFormatter,\r\n  labelClassName,\r\n  formatter,\r\n  color,\r\n  nameKey,\r\n  labelKey,\r\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n  React.ComponentProps<\"div\"> & {\r\n    hideLabel?: boolean\r\n    hideIndicator?: boolean\r\n    indicator?: \"line\" | \"dot\" | \"dashed\"\r\n    nameKey?: string\r\n    labelKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  const tooltipLabel = React.useMemo(() => {\r\n    if (hideLabel || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const [item] = payload\r\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\r\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n    const value =\r\n      !labelKey && typeof label === \"string\"\r\n        ? config[label as keyof typeof config]?.label || label\r\n        : itemConfig?.label\r\n\r\n    if (labelFormatter) {\r\n      return (\r\n        <div className={cn(\"font-medium\", labelClassName)}>\r\n          {labelFormatter(value, payload)}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (!value) {\r\n      return null\r\n    }\r\n\r\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n  }, [\r\n    label,\r\n    labelFormatter,\r\n    payload,\r\n    hideLabel,\r\n    labelClassName,\r\n    config,\r\n    labelKey,\r\n  ])\r\n\r\n  if (!active || !payload?.length) {\r\n    return null\r\n  }\r\n\r\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\r\n        className\r\n      )}\r\n    >\r\n      {!nestLabel ? tooltipLabel : null}\r\n      <div className=\"grid gap-1.5\">\r\n        {payload.map((item, index) => {\r\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n          const indicatorColor = color || item.payload.fill || item.color\r\n\r\n          return (\r\n            <div\r\n              key={item.dataKey}\r\n              className={cn(\r\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\r\n                indicator === \"dot\" && \"items-center\"\r\n              )}\r\n            >\r\n              {formatter && item?.value !== undefined && item.name ? (\r\n                formatter(item.value, item.name, item, index, item.payload)\r\n              ) : (\r\n                <>\r\n                  {itemConfig?.icon ? (\r\n                    <itemConfig.icon />\r\n                  ) : (\r\n                    !hideIndicator && (\r\n                      <div\r\n                        className={cn(\r\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\r\n                          {\r\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                            \"w-1\": indicator === \"line\",\r\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                              indicator === \"dashed\",\r\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                          }\r\n                        )}\r\n                        style={\r\n                          {\r\n                            \"--color-bg\": indicatorColor,\r\n                            \"--color-border\": indicatorColor,\r\n                          } as React.CSSProperties\r\n                        }\r\n                      />\r\n                    )\r\n                  )}\r\n                  <div\r\n                    className={cn(\r\n                      \"flex flex-1 justify-between leading-none\",\r\n                      nestLabel ? \"items-end\" : \"items-center\"\r\n                    )}\r\n                  >\r\n                    <div className=\"grid gap-1.5\">\r\n                      {nestLabel ? tooltipLabel : null}\r\n                      <span className=\"text-muted-foreground\">\r\n                        {itemConfig?.label || item.name}\r\n                      </span>\r\n                    </div>\r\n                    {item.value && (\r\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\r\n                        {item.value.toLocaleString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nfunction ChartLegendContent({\r\n  className,\r\n  hideIcon = false,\r\n  payload,\r\n  verticalAlign = \"bottom\",\r\n  nameKey,\r\n}: React.ComponentProps<\"div\"> &\r\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n    hideIcon?: boolean\r\n    nameKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  if (!payload?.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex items-center justify-center gap-4\",\r\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n        className\r\n      )}\r\n    >\r\n      {payload.map((item) => {\r\n        const key = `${nameKey || item.dataKey || \"value\"}`\r\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n        return (\r\n          <div\r\n            key={item.value}\r\n            className={cn(\r\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\r\n            )}\r\n          >\r\n            {itemConfig?.icon && !hideIcon ? (\r\n              <itemConfig.icon />\r\n            ) : (\r\n              <div\r\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                style={{\r\n                  backgroundColor: item.color,\r\n                }}\r\n              />\r\n            )}\r\n            {itemConfig?.label}\r\n          </div>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;;;AALA;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,6JAAA,CAAA,gBAAmB,CAA2B;AAEnE,SAAS;;IACP,MAAM,UAAU,6JAAA,CAAA,aAAgB,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,SAAS,eAAe,KAWvB;QAXuB,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OAMJ,GAXuB;;IAYtB,MAAM,WAAW,6JAAA,CAAA,QAAW;IAC5B,MAAM,UAAU,AAAC,SAAyC,OAAjC,MAAM,SAAS,OAAO,CAAC,MAAM;IAEtD,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,6LAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,6LAAC,sKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;IAjCS;KAAA;AAmCT,MAAM,aAAa;QAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C;YAAC,GAAG,OAAO;eAAK,OAAO,KAAK,IAAI,OAAO,KAAK;;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF;oBAAC,CAAC,OAAO,OAAO;uBAAK,AAAC,KACV,OAAtB,QAAO,iBACP,OADsB,IAAG,SAQb,OAPZ,YACC,GAAG,CAAC;wBAAC,CAAC,KAAK,WAAW;wBAEnB;oBADF,MAAM,QACJ,EAAA,oBAAA,WAAW,KAAK,cAAhB,wCAAA,iBAAkB,CAAC,MAAuC,KAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,AAAC,aAAoB,OAAR,KAAI,MAAU,OAAN,OAAM,OAAK;gBACjD,GACC,IAAI,CAAC,OAAM;eAIH,IAAI,CAAC;QACV;;;;;;AAGN;MA/BM;AAiCN,MAAM,eAAe,0JAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,KAqB1B;QArB0B,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EAQP,GArB0B;;IAsB3B,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,6JAAA,CAAA,UAAa;qDAAC;gBAU3B;YATN,IAAI,aAAa,EAAC,oBAAA,8BAAA,QAAS,MAAM,GAAE;gBACjC,OAAO;YACT;YAEA,MAAM,CAAC,KAAK,GAAG;YACf,MAAM,MAAM,AAAC,GAAqD,OAAnD,aAAY,iBAAA,2BAAA,KAAM,OAAO,MAAI,iBAAA,2BAAA,KAAM,IAAI,KAAI;YAC1D,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,EAAA,gBAAA,MAAM,CAAC,MAA6B,cAApC,oCAAA,cAAsC,KAAK,KAAI,QAC/C,uBAAA,iCAAA,WAAY,KAAK;YAEvB,IAAI,gBAAgB;gBAClB,qBACE,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;8BAC/B,eAAe,OAAO;;;;;;YAG7B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,qBAAO,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAAkB;;;;;;QAC7D;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,EAAC,oBAAA,8BAAA,QAAS,MAAM,GAAE;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,AAAC,GAAkD,OAAhD,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI;oBACvD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,CAAA,iBAAA,2BAAA,KAAM,KAAK,MAAK,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,CAAA,uBAAA,iCAAA,WAAY,IAAI,kBACf,6LAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,6LAAC;oDAAK,WAAU;8DACb,CAAA,uBAAA,iCAAA,WAAY,KAAK,KAAI,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;IA9IS;;QAsBY;;;MAtBZ;AAgJT,MAAM,cAAc,yJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,KAUzB;QAVyB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EAKN,GAVyB;;IAW1B,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,EAAC,oBAAA,8BAAA,QAAS,MAAM,GAAE;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,AAAC,GAAqC,OAAnC,WAAW,KAAK,OAAO,IAAI;YAC1C,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,CAAA,uBAAA,iCAAA,WAAY,IAAI,KAAI,CAAC,yBACpB,6LAAC,WAAW,IAAI;;;;6CAEhB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,uBAAA,iCAAA,WAAY,KAAK;;eAfb,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;IApDS;;QAWY;;;MAXZ;AAsDT,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/charts/MoodTrendChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from \"recharts\";\nimport { ChartContainer, ChartTooltip, ChartTooltipContent } from \"@/components/ui/chart\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\n\ninterface ChartDataPoint {\n  day: string;\n  value: number;\n}\n\ninterface MoodTrendChartProps {\n  data: ChartDataPoint[];\n}\n\nconst chartConfig = {\n  value: {\n    label: \"Mood Level\",\n    color: \"#14b8a6\",\n  },\n};\n\nexport function MoodTrendChart({ data }: MoodTrendChartProps) {\n  return (\n    <Card className=\"health-card-shadow health-rounded\">\n      <CardHeader className=\"pb-2\">\n        <CardTitle className=\"health-heading-sm\">Mood Trend (Last 7 days)</CardTitle>\n      </CardHeader>\n      <CardContent className=\"h-80\">\n        <ChartContainer config={chartConfig} className=\"w-full h-full\">\n          <Line<PERSON><PERSON> data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>\n            <XAxis \n              dataKey=\"day\" \n              axisLine={false}\n              tickLine={false}\n              tick={{ fontSize: 12, fill: \"#868686\" }}\n            />\n            <YAxis \n              axisLine={false}\n              tickLine={false}\n              tick={{ fontSize: 12, fill: \"#868686\" }}\n              domain={[0, 100]}\n            />\n            <ChartTooltip content={<ChartTooltipContent />} />\n            <Line \n              type=\"monotone\" \n              dataKey=\"value\" \n              stroke=\"#14b8a6\" \n              strokeWidth={3}\n              dot={{ fill: \"#14b8a6\", strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, fill: \"#14b8a6\" }}\n            />\n          </LineChart>\n        </ChartContainer>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAeA,MAAM,cAAc;IAClB,OAAO;QACL,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS,eAAe,KAA6B;QAA7B,EAAE,IAAI,EAAuB,GAA7B;IAC7B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAoB;;;;;;;;;;;0BAE3C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC,oIAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;wBAAM,QAAQ;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAI,QAAQ;wBAAG;;0CACxE,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,UAAU;oCAAI,MAAM;gCAAU;;;;;;0CAExC,6LAAC,wJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,UAAU;oCAAI,MAAM;gCAAU;gCACtC,QAAQ;oCAAC;oCAAG;iCAAI;;;;;;0CAElB,6LAAC,oIAAA,CAAA,eAAY;gCAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;;;;;;;;;;0CAC3C,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KAnCgB", "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/charts/PainLevelLineChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"recharts\";\nimport { <PERSON><PERSON><PERSON>r, ChartTooltip, ChartTooltipContent } from \"@/components/ui/chart\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\n\ninterface ChartDataPoint {\n  day: string;\n  value: number;\n}\n\ninterface PainLevelLineChartProps {\n  data: ChartDataPoint[];\n}\n\nconst chartConfig = {\n  value: {\n    label: \"Pain Level\",\n    color: \"#f97316\",\n  },\n};\n\nexport function PainLevelLineChart({ data }: PainLevelLineChartProps) {\n  return (\n    <Card className=\"health-card-shadow health-rounded\">\n      <CardHeader className=\"pb-2\">\n        <CardTitle className=\"health-heading-sm\">Pain Level Over Time</CardTitle>\n      </CardHeader>\n      <CardContent className=\"h-80\">\n        <ChartContainer config={chartConfig} className=\"w-full h-full\">\n          <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>\n            <XAxis \n              dataKey=\"day\" \n              axisLine={false}\n              tickLine={false}\n              tick={{ fontSize: 12, fill: \"#868686\" }}\n            />\n            <YAxis \n              axisLine={false}\n              tickLine={false}\n              tick={{ fontSize: 12, fill: \"#868686\" }}\n              domain={[0, 100]}\n            />\n            <ChartTooltip content={<ChartTooltipContent />} />\n            <Line \n              type=\"monotone\" \n              dataKey=\"value\" \n              stroke=\"#f97316\" \n              strokeWidth={3}\n              dot={{ fill: \"#f97316\", strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, fill: \"#f97316\" }}\n            />\n          </LineChart>\n        </ChartContainer>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAeA,MAAM,cAAc;IAClB,OAAO;QACL,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS,mBAAmB,KAAiC;QAAjC,EAAE,IAAI,EAA2B,GAAjC;IACjC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAoB;;;;;;;;;;;0BAE3C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC,oIAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;wBAAM,QAAQ;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAI,QAAQ;wBAAG;;0CACxE,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,UAAU;oCAAI,MAAM;gCAAU;;;;;;0CAExC,6LAAC,wJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,UAAU;oCAAI,MAAM;gCAAU;gCACtC,QAAQ;oCAAC;oCAAG;iCAAI;;;;;;0CAElB,6LAAC,oIAAA,CAAA,eAAY;gCAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;;;;;;;;;;0CAC3C,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KAnCgB", "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/charts/PainLevelBarChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"recharts\";\nimport { <PERSON><PERSON><PERSON>r, ChartTooltip, ChartTooltipContent } from \"@/components/ui/chart\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\n\ninterface ChartDataPoint {\n  day: string;\n  value: number;\n}\n\ninterface PainLevelBarChartProps {\n  data: ChartDataPoint[];\n}\n\nconst chartConfig = {\n  value: {\n    label: \"Pain Level\",\n    color: \"#3b82f6\",\n  },\n};\n\nexport function PainLevelBarChart({ data }: PainLevelBarChartProps) {\n  return (\n    <Card className=\"health-card-shadow health-rounded\">\n      <CardHeader className=\"pb-2\">\n        <CardTitle className=\"health-heading-sm\">Pain Level Over Time</CardTitle>\n      </CardHeader>\n      <CardContent className=\"h-80\">\n        <ChartContainer config={chartConfig} className=\"w-full h-full\">\n          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>\n            <XAxis \n              dataKey=\"day\" \n              axisLine={false}\n              tickLine={false}\n              tick={{ fontSize: 12, fill: \"#868686\" }}\n            />\n            <YAxis \n              axisLine={false}\n              tickLine={false}\n              tick={{ fontSize: 12, fill: \"#868686\" }}\n              domain={[0, 100]}\n            />\n            <ChartTooltip content={<ChartTooltipContent />} />\n            <Bar \n              dataKey=\"value\" \n              fill=\"#3b82f6\"\n              radius={[2, 2, 0, 0]}\n            />\n          </BarChart>\n        </ChartContainer>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAeA,MAAM,cAAc;IAClB,OAAO;QACL,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS,kBAAkB,KAAgC;QAAhC,EAAE,IAAI,EAA0B,GAAhC;IAChC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAoB;;;;;;;;;;;0BAE3C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC,oIAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAM,QAAQ;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAI,QAAQ;wBAAG;;0CACvE,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,UAAU;oCAAI,MAAM;gCAAU;;;;;;0CAExC,6LAAC,wJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,UAAU;oCAAI,MAAM;gCAAU;gCACtC,QAAQ;oCAAC;oCAAG;iCAAI;;;;;;0CAElB,6LAAC,oIAAA,CAAA,eAAY;gCAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;;;;;;;;;;0CAC3C,6LAAC,sJAAA,CAAA,MAAG;gCACF,SAAQ;gCACR,MAAK;gCACL,QAAQ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;KAhCgB", "debugId": null}}, {"offset": {"line": 2140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/charts/CircularProgressChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\n\ninterface CircularProgressData {\n  value: number;\n  total: number;\n}\n\ninterface CircularProgressChartProps {\n  data: CircularProgressData;\n}\n\nexport function CircularProgressChart({ data }: CircularProgressChartProps) {\n  const percentage = (data.value / data.total) * 100;\n  const circumference = 2 * Math.PI * 45; // radius = 45\n  const strokeDasharray = circumference;\n  const strokeDashoffset = circumference - (percentage / 100) * circumference;\n\n  return (\n    <Card className=\"health-card-shadow health-rounded\">\n      <CardHeader className=\"pb-2\">\n        <CardTitle className=\"health-heading-sm\">Pain Level Over Time</CardTitle>\n      </CardHeader>\n      <CardContent className=\"h-80 flex items-center justify-center\">\n        <div className=\"relative w-32 h-32\">\n          <svg className=\"w-32 h-32 transform -rotate-90\" viewBox=\"0 0 100 100\">\n            {/* Background circle */}\n            <circle\n              cx=\"50\"\n              cy=\"50\"\n              r=\"45\"\n              stroke=\"#e5e7eb\"\n              strokeWidth=\"8\"\n              fill=\"none\"\n            />\n            {/* Progress circle */}\n            <circle\n              cx=\"50\"\n              cy=\"50\"\n              r=\"45\"\n              stroke=\"#455f84\"\n              strokeWidth=\"8\"\n              fill=\"none\"\n              strokeDasharray={strokeDasharray}\n              strokeDashoffset={strokeDashoffset}\n              strokeLinecap=\"round\"\n              className=\"transition-all duration-300 ease-in-out\"\n            />\n          </svg>\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <span className=\"text-4xl font-semibold text-health-secondary\">\n              {data.value}%\n            </span>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaO,SAAS,sBAAsB,KAAoC;QAApC,EAAE,IAAI,EAA8B,GAApC;IACpC,MAAM,aAAa,AAAC,KAAK,KAAK,GAAG,KAAK,KAAK,GAAI;IAC/C,MAAM,gBAAgB,IAAI,KAAK,EAAE,GAAG,IAAI,cAAc;IACtD,MAAM,kBAAkB;IACxB,MAAM,mBAAmB,gBAAgB,AAAC,aAAa,MAAO;IAE9D,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAoB;;;;;;;;;;;0BAE3C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAiC,SAAQ;;8CAEtD,6LAAC;oCACC,IAAG;oCACH,IAAG;oCACH,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;;;;;;8CAGP,6LAAC;oCACC,IAAG;oCACH,IAAG;oCACH,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,iBAAiB;oCACjB,kBAAkB;oCAClB,eAAc;oCACd,WAAU;;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;KA9CgB", "debugId": null}}, {"offset": {"line": 2263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/dashboard/ChartsGrid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MoodTrendChart } from \"./charts/MoodTrendChart\";\nimport { PainLevelLineChart } from \"./charts/PainLevelLineChart\";\nimport { PainLevelBarChart } from \"./charts/PainLevelBarChart\";\nimport { CircularProgressChart } from \"./charts/CircularProgressChart\";\n\ninterface ChartData {\n  moodTrend: Array<{ day: string; value: number }>;\n  painLevelLine: Array<{ day: string; value: number }>;\n  painLevelBar: Array<{ day: string; value: number }>;\n  painLevelCircular: { value: number; total: number };\n}\n\ninterface ChartsGridProps {\n  chartData: ChartData;\n}\n\nexport function ChartsGrid({ chartData }: ChartsGridProps) {\n  return (\n    <section className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n      <MoodTrendChart data={chartData.moodTrend} />\n      <PainLevelLineChart data={chartData.painLevelLine} />\n      <PainLevelBarChart data={chartData.painLevelBar} />\n      <CircularProgressChart data={chartData.painLevelCircular} />\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAkBO,SAAS,WAAW,KAA8B;QAA9B,EAAE,SAAS,EAAmB,GAA9B;IACzB,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC,8JAAA,CAAA,iBAAc;gBAAC,MAAM,UAAU,SAAS;;;;;;0BACzC,6LAAC,kKAAA,CAAA,qBAAkB;gBAAC,MAAM,UAAU,aAAa;;;;;;0BACjD,6LAAC,iKAAA,CAAA,oBAAiB;gBAAC,MAAM,UAAU,YAAY;;;;;;0BAC/C,6LAAC,qKAAA,CAAA,wBAAqB;gBAAC,MAAM,UAAU,iBAAiB;;;;;;;;;;;;AAG9D;KATgB", "debugId": null}}, {"offset": {"line": 2329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { PersonalizedHeader } from \"@/components/dashboard/PersonalizedHeader\";\r\nimport { HealthMetricsGrid } from \"@/components/dashboard/HealthMetricsGrid\";\r\nimport { QuickMoodSection } from \"@/components/dashboard/QuickMoodSection\";\r\nimport { QuickActionsSection } from \"@/components/dashboard/QuickActionsSection\";\r\nimport { ActivityTimeline } from \"@/components/dashboard/ActivityTimeline\";\r\nimport { ChartsGrid } from \"@/components/dashboard/ChartsGrid\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { mockRootProps } from \"./healthDashboardMockData\";\r\n\r\nexport default function HealthDashboard() {\r\n  const { user, healthMetrics, quickActions, recentActivities, chartData } = mockRootProps;\r\n\r\n  return (\r\n    <div className=\"w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background\">\r\n      <PersonalizedHeader \r\n        userName={user.name} \r\n        currentDate={user.currentDate} \r\n      />\r\n      \r\n      <HealthMetricsGrid metrics={healthMetrics} />\r\n      \r\n      <QuickMoodSection />\r\n      \r\n      <QuickActionsSection actions={quickActions} />\r\n      \r\n      <ActivityTimeline activities={recentActivities} />\r\n      \r\n      <ChartsGrid chartData={chartData} />\r\n      \r\n      <Separator />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AARA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,GAAG;IAE3E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wJAAA,CAAA,qBAAkB;gBACjB,UAAU,KAAK,IAAI;gBACnB,aAAa,KAAK,WAAW;;;;;;0BAG/B,6LAAC,uJAAA,CAAA,oBAAiB;gBAAC,SAAS;;;;;;0BAE5B,6LAAC,sJAAA,CAAA,mBAAgB;;;;;0BAEjB,6LAAC,yJAAA,CAAA,sBAAmB;gBAAC,SAAS;;;;;;0BAE9B,6LAAC,sJAAA,CAAA,mBAAgB;gBAAC,YAAY;;;;;;0BAE9B,6LAAC,gJAAA,CAAA,aAAU;gBAAC,WAAW;;;;;;0BAEvB,6LAAC,wIAAA,CAAA,YAAS;;;;;;;;;;;AAGhB;KAvBwB", "debugId": null}}]}