import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface ActivityGuidanceProps {
  recommendedLevel: string;
  description: string;
  restBreaks: string;
  restDescription: string;
}

export function ActivityGuidance({ 
  recommendedLevel, 
  description, 
  restBreaks, 
  restDescription 
}: ActivityGuidanceProps) {
  return (
    <div className="space-y-4">
      <h2 className="health-heading-sm">Pacing & Activity Guidance</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-white p-6">
          <CardContent className="p-0 space-y-2">
            <p className="health-caption text-health-accent">Today's Recommended Activity Level</p>
            <p className="health-heading-sm text-health-accent font-medium">{recommendedLevel}</p>
            <p className="health-body-sm text-health-accent">{description}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-white p-6">
          <CardContent className="p-0 space-y-2">
            <p className="health-caption text-health-accent">Suggested Rest Breaks</p>
            <p className="health-heading-sm text-health-accent font-medium">{restBreaks}</p>
            <p className="health-body-sm text-health-accent">{restDescription}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}