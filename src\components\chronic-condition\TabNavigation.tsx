import React from 'react';
import { Tabs, Tabs<PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';

const tabItems = [
  { id: 'pacing', label: 'Pacing & Activity' },
  { id: 'medication', label: 'Medication Regimen' },
  { id: 'trigger', label: 'Trigger Management' },
  { id: 'progress', label: 'Progress Tracking' }
];

export function TabNavigation() {
  return (
    <Tabs defaultValue="pacing" className="w-full">
      <TabsList className="grid w-full grid-cols-4 bg-transparent p-0 h-auto">
        {tabItems.map((tab) => (
          <TabsTrigger 
            key={tab.id}
            value={tab.id}
            className="health-body-lg text-health-muted data-[state=active]:text-health-accent data-[state=active]:border-b-2 data-[state=active]:border-health-accent rounded-none bg-transparent px-0 py-2"
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {tabItems.map((tab) => (
        <TabsContent key={tab.id} value={tab.id} className="mt-4">
          {/* Tab content will be rendered here based on active tab */}
        </TabsContent>
      ))}
    </Tabs>
  );
}