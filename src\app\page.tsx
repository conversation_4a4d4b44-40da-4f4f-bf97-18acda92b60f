"use client";

import { PersonalizedHeader } from "@/components/dashboard/PersonalizedHeader";
import { HealthMetricsGrid } from "@/components/dashboard/HealthMetricsGrid";
import { QuickMoodSection } from "@/components/dashboard/QuickMoodSection";
import { QuickActionsSection } from "@/components/dashboard/QuickActionsSection";
import { ActivityTimeline } from "@/components/dashboard/ActivityTimeline";
import { ChartsGrid } from "@/components/dashboard/ChartsGrid";
import { Separator } from "@/components/ui/separator";
import { mockRootProps } from "@/mocks/healthDashboardMockData";

export default function HealthDashboard() {
  const { user, healthMetrics, quickActions, recentActivities, chartData } = mockRootProps;

  return (
    <div className="w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background">
      <PersonalizedHeader 
        userName={user.name} 
        currentDate={user.currentDate} 
      />
      
      <HealthMetricsGrid metrics={healthMetrics} />
      
      <QuickMoodSection />
      
      <QuickActionsSection actions={quickActions} />
      
      <ActivityTimeline activities={recentActivities} />
      
      <ChartsGrid chartData={chartData} />
      
      <Separator />
    </div>
  );
}