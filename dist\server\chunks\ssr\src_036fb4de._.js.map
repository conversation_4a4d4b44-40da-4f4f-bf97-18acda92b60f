{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/SymptomOverviewCard.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\n\ninterface SymptomOverviewCardProps {\n  title: string;\n  value: string;\n  variant?: \"primary\" | \"secondary\";\n}\n\nexport function SymptomOverviewCard({ title, value, variant = \"primary\" }: SymptomOverviewCardProps) {\n  return (\n    <Card className=\"health-card-shadow health-rounded p-6\">\n      <CardContent className=\"p-0 space-y-2\">\n        <h3 className=\"health-body-lg\">{title}</h3>\n        <p className={variant === \"primary\" ? \"health-body-sm\" : \"health-body-sm\"}>\n          {value}\n        </p>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUO,SAAS,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,SAAS,EAA4B;IACjG,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAG,WAAU;8BAAkB;;;;;;8BAChC,8OAAC;oBAAE,WAAW,YAAY,YAAY,mBAAmB;8BACtD;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/symptomAnalysisMockData.ts"], "sourcesContent": ["// Enums for symptom analysis page\nexport enum SeverityLevel {\n  MILD = \"Mild\",\n  MODERATE = \"Moderate\",\n  SEVERE = \"Severe\"\n}\n\nexport enum ConditionType {\n  TENSION_HEADACHE = \"Tension Headache\",\n  MIGRAINE = \"Migraine\",\n  CLUSTER_HEADACHE = \"Cluster Headache\"\n}\n\nexport enum TimeRange {\n  LAST_7_DAYS = \"Last 7 days\",\n  LAST_30_DAYS = \"Last 30 days\",\n  LAST_3_MONTHS = \"Last 3 months\"\n}\n\n// String formatters\nexport const formatDate = (date: Date): string => {\n  return date.toLocaleDateString('en-US', { \n    month: 'long', \n    day: 'numeric', \n    year: 'numeric' \n  });\n};\n\nexport const formatTime = (date: Date): string => {\n  return date.toLocaleTimeString('en-US', { \n    hour: 'numeric', \n    minute: '2-digit', \n    hour12: true \n  });\n};\n\nexport const formatPercentage = (value: number): string => {\n  return `${value}%`;\n};\n\nexport const formatBPM = (value: number): string => {\n  return `${value} BPM`;\n};\n\nexport const formatHours = (value: number): string => {\n  return `${value} hours`;\n};\n\n// Data passed as props to the root component\nexport const mockRootProps = {\n  symptomData: {\n    type: \"Headache\" as const,\n    severity: SeverityLevel.MILD,\n    detectedDate: new Date(\"2025-05-24\"),\n    detectedTime: new Date(\"2025-05-24T10:30:00\"),\n    associatedFactors: [\"Stress\", \"Lack of Sleep\"] as const\n  },\n  aiInsights: {\n    heartRate: 72,\n    airQuality: \"Moderate\" as const,\n    sleepHours: 6,\n    analysisText: \"The observed symptom patterns indicate potential early warning signs. It's important to analyze the trends closely and implement the suggested actions to address any issues.\"\n  },\n  potentialConditions: [\n    {\n      condition: ConditionType.TENSION_HEADACHE,\n      probability: 70\n    },\n    {\n      condition: ConditionType.MIGRAINE,\n      probability: 25\n    }\n  ],\n  healthAlerts: [\n    \"High pollen count today, may exacerbate allergies\"\n  ],\n  recommendations: [\n    \"Monitor for 24 hours\",\n    \"Consider over the counter pain relief\", \n    \"If symptoms worsen, consult a doctor\"\n  ],\n  chartData: [\n    { day: \"Mon\", value: 2, dayIndex: 0 },\n    { day: \"Tue\", value: 3, dayIndex: 1 },\n    { day: \"Wed\", value: 1, dayIndex: 2 },\n    { day: \"Thur\", value: 4, dayIndex: 3 },\n    { day: \"Fri\", value: 6, dayIndex: 4 },\n    { day: \"Sat\", value: 3, dayIndex: 5 },\n    { day: \"Sun\", value: 2, dayIndex: 6 }\n  ],\n  historicalData: [\n    {\n      dateLogged: new Date(\"2025-06-10\"),\n      severity: SeverityLevel.MODERATE,\n      aiSummary: \"Likely due to fatigue, monitor hydration\"\n    },\n    {\n      dateLogged: new Date(\"2025-06-10\"),\n      severity: SeverityLevel.MODERATE,\n      aiSummary: \"Likely due to fatigue, monitor hydration\"\n    },\n    {\n      dateLogged: new Date(\"2025-06-10\"),\n      severity: SeverityLevel.MODERATE,\n      aiSummary: \"Likely due to fatigue, monitor hydration\"\n    }\n  ]\n};\n\n// Props types (data passed to components)\nexport interface SymptomData {\n  type: string;\n  severity: string;\n  detectedDate: Date;\n  detectedTime: Date;\n  associatedFactors: string[];\n}\n\nexport interface AIInsights {\n  heartRate: number;\n  airQuality: string;\n  sleepHours: number;\n  analysisText: string;\n}\n\nexport interface PotentialCondition {\n  condition: string;\n  probability: number;\n}\n\nexport interface ChartDataPoint {\n  day: string;\n  value: number;\n  dayIndex: number;\n}\n\nexport interface HistoricalEntry {\n  dateLogged: Date;\n  severity: string;\n  aiSummary: string;\n}\n\nexport interface SymptomAnalysisProps {\n  symptomData: SymptomData;\n  aiInsights: AIInsights;\n  potentialConditions: PotentialCondition[];\n  healthAlerts: string[];\n  recommendations: string[];\n  chartData: ChartDataPoint[];\n  historicalData: HistoricalEntry[];\n}"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;;;;;;;AAC3B,IAAA,AAAK,uCAAA;;;;WAAA;;AAML,IAAA,AAAK,uCAAA;;;;WAAA;;AAML,IAAA,AAAK,mCAAA;;;;WAAA;;AAOL,MAAM,aAAa,CAAC;IACzB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,OAAO;QACP,KAAK;QACL,MAAM;IACR;AACF;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,GAAG,MAAM,CAAC,CAAC;AACpB;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,GAAG,MAAM,IAAI,CAAC;AACvB;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,GAAG,MAAM,MAAM,CAAC;AACzB;AAGO,MAAM,gBAAgB;IAC3B,aAAa;QACX,MAAM;QACN,QAAQ;QACR,cAAc,IAAI,KAAK;QACvB,cAAc,IAAI,KAAK;QACvB,mBAAmB;YAAC;YAAU;SAAgB;IAChD;IACA,YAAY;QACV,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,cAAc;IAChB;IACA,qBAAqB;QACnB;YACE,SAAS;YACT,aAAa;QACf;QACA;YACE,SAAS;YACT,aAAa;QACf;KACD;IACD,cAAc;QACZ;KACD;IACD,iBAAiB;QACf;QACA;QACA;KACD;IACD,WAAW;QACT;YAAE,KAAK;YAAO,OAAO;YAAG,UAAU;QAAE;QACpC;YAAE,KAAK;YAAO,OAAO;YAAG,UAAU;QAAE;QACpC;YAAE,KAAK;YAAO,OAAO;YAAG,UAAU;QAAE;QACpC;YAAE,KAAK;YAAQ,OAAO;YAAG,UAAU;QAAE;QACrC;YAAE,KAAK;YAAO,OAAO;YAAG,UAAU;QAAE;QACpC;YAAE,KAAK;YAAO,OAAO;YAAG,UAAU;QAAE;QACpC;YAAE,KAAK;YAAO,OAAO;YAAG,UAAU;QAAE;KACrC;IACD,gBAAgB;QACd;YACE,YAAY,IAAI,KAAK;YACrB,QAAQ;YACR,WAAW;QACb;QACA;YACE,YAAY,IAAI,KAAK;YACrB,QAAQ;YACR,WAAW;QACb;QACA;YACE,YAAY,IAAI,KAAK;YACrB,QAAQ;YACR,WAAW;QACb;KACD;AACH", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/AIInsightSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { formatBPM, formatHours } from \"@/app/symptomAnalysisMockData\";\n\ninterface AIInsightSectionProps {\n  heartRate: number;\n  airQuality: string;\n  sleepHours: number;\n  analysisText: string;\n}\n\nexport function AIInsightSection({ heartRate, airQuality, sleepHours, analysisText }: AIInsightSectionProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-lg\">AI Insight</h2>\n      \n      <div className=\"space-y-4\">\n        <h3 className=\"health-heading-sm\">AI Generated Contextual Data</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Card className=\"health-card-shadow health-rounded p-4\">\n            <CardContent className=\"p-0 space-y-2\">\n              <p className=\"health-body-lg\">Your recent heart rate from wearable:</p>\n              <p className=\"health-heading-sm\">{formatBPM(heartRate)}</p>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"health-card-shadow health-rounded p-4\">\n            <CardContent className=\"p-0 space-y-2\">\n              <p className=\"health-body-lg\">Local air quality</p>\n              <p className=\"health-heading-sm\">{airQuality}</p>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"health-card-shadow health-rounded p-4\">\n            <CardContent className=\"p-0 space-y-2\">\n              <p className=\"health-body-lg\">Last logged sleep</p>\n              <p className=\"health-heading-sm\">{formatHours(sleepHours)}</p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n      \n      <div className=\"space-y-4\">\n        <h3 className=\"health-heading-sm\">AI Analysis Summary</h3>\n        <div className=\"space-y-4\">\n          <p className=\"health-body-lg\">{analysisText}</p>\n          <p className=\"health-body-lg\">{analysisText}</p>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAyB;IACzG,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoB;;;;;;kCAElC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;sDAC9B,8OAAC;4CAAE,WAAU;sDAAqB,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD,EAAE;;;;;;;;;;;;;;;;;0CAIhD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;sDAC9B,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAItC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;sDAC9B,8OAAC;4CAAE,WAAU;sDAAqB,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoB;;;;;;kCAClC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;0CAC/B,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/PotentialConditionsCard.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { formatPercentage } from \"@/app/symptomAnalysisMockData\";\n\ninterface PotentialCondition {\n  condition: string;\n  probability: number;\n}\n\ninterface PotentialConditionsCardProps {\n  conditions: PotentialCondition[];\n}\n\nexport function PotentialConditionsCard({ conditions }: PotentialConditionsCardProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h3 className=\"health-heading-sm\">Potential Conditions</h3>\n      <div className=\"space-y-4\">\n        {conditions.map((condition, index) => (\n          <Card key={index} className=\"health-card-shadow health-rounded p-4\">\n            <CardContent className=\"p-0 space-y-2\">\n              <p className=\"health-body-lg\">{condition.condition}</p>\n              <p className=\"health-heading-sm\">{formatPercentage(condition.probability)}</p>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcO,SAAS,wBAAwB,EAAE,UAAU,EAAgC;IAClF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAClC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;kCAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAE,WAAU;8CAAkB,UAAU,SAAS;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAqB,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,WAAW;;;;;;;;;;;;uBAHjE;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/RecommendationsSection.tsx"], "sourcesContent": ["\"use client\";\n\ninterface RecommendationsSectionProps {\n  recommendations: string[];\n  healthAlerts: string[];\n}\n\nexport function RecommendationsSection({ recommendations, healthAlerts }: RecommendationsSectionProps) {\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <h3 className=\"health-heading-sm\">Localized Health Alerts</h3>\n        {healthAlerts.map((alert, index) => (\n          <p key={index} className=\"health-body-lg\">{alert}</p>\n        ))}\n      </div>\n      \n      <div className=\"space-y-4\">\n        <h3 className=\"health-heading-sm\">Recommendations</h3>\n        <div className=\"space-y-4\">\n          {recommendations.map((recommendation, index) => (\n            <p key={index} className=\"health-body-lg\">{recommendation}</p>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;;AAOO,SAAS,uBAAuB,EAAE,eAAe,EAAE,YAAY,EAA+B;IACnG,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoB;;;;;;oBACjC,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;4BAAc,WAAU;sCAAkB;2BAAnC;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoB;;;;;;kCAClC,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,sBACpC,8OAAC;gCAAc,WAAU;0CAAkB;+BAAnC;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/QuickActionsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\n\nexport function QuickActionsSection() {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-lg\">Quick Actions</h2>\n      \n      <div className=\"flex gap-5\">\n        <Button \n          variant=\"link\" \n          className=\"px-0 health-button-text\"\n          onClick={() => console.log(\"Log Follow up Symptom clicked\")}\n        >\n          Log Follow up Symptom\n        </Button>\n        \n        <Button \n          variant=\"link\" \n          className=\"px-0 health-button-text\"\n          onClick={() => console.log(\"Chat with AI Assistant clicked\")}\n        >\n          Chat with AI Assistant\n        </Button>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,QAAQ,GAAG,CAAC;kCAC5B;;;;;;kCAID,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,QAAQ,GAAG,CAAC;kCAC5B;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [k in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction ChartContainer({\r\n  id,\r\n  className,\r\n  children,\r\n  config,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  config: ChartConfig\r\n  children: React.ComponentProps<\r\n    typeof RechartsPrimitive.ResponsiveContainer\r\n  >[\"children\"]\r\n}) {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-slot=\"chart\"\r\n        data-chart={chartId}\r\n        className={cn(\r\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n}\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nfunction ChartTooltipContent({\r\n  active,\r\n  payload,\r\n  className,\r\n  indicator = \"dot\",\r\n  hideLabel = false,\r\n  hideIndicator = false,\r\n  label,\r\n  labelFormatter,\r\n  labelClassName,\r\n  formatter,\r\n  color,\r\n  nameKey,\r\n  labelKey,\r\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n  React.ComponentProps<\"div\"> & {\r\n    hideLabel?: boolean\r\n    hideIndicator?: boolean\r\n    indicator?: \"line\" | \"dot\" | \"dashed\"\r\n    nameKey?: string\r\n    labelKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  const tooltipLabel = React.useMemo(() => {\r\n    if (hideLabel || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const [item] = payload\r\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\r\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n    const value =\r\n      !labelKey && typeof label === \"string\"\r\n        ? config[label as keyof typeof config]?.label || label\r\n        : itemConfig?.label\r\n\r\n    if (labelFormatter) {\r\n      return (\r\n        <div className={cn(\"font-medium\", labelClassName)}>\r\n          {labelFormatter(value, payload)}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (!value) {\r\n      return null\r\n    }\r\n\r\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n  }, [\r\n    label,\r\n    labelFormatter,\r\n    payload,\r\n    hideLabel,\r\n    labelClassName,\r\n    config,\r\n    labelKey,\r\n  ])\r\n\r\n  if (!active || !payload?.length) {\r\n    return null\r\n  }\r\n\r\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\r\n        className\r\n      )}\r\n    >\r\n      {!nestLabel ? tooltipLabel : null}\r\n      <div className=\"grid gap-1.5\">\r\n        {payload.map((item, index) => {\r\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n          const indicatorColor = color || item.payload.fill || item.color\r\n\r\n          return (\r\n            <div\r\n              key={item.dataKey}\r\n              className={cn(\r\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\r\n                indicator === \"dot\" && \"items-center\"\r\n              )}\r\n            >\r\n              {formatter && item?.value !== undefined && item.name ? (\r\n                formatter(item.value, item.name, item, index, item.payload)\r\n              ) : (\r\n                <>\r\n                  {itemConfig?.icon ? (\r\n                    <itemConfig.icon />\r\n                  ) : (\r\n                    !hideIndicator && (\r\n                      <div\r\n                        className={cn(\r\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\r\n                          {\r\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                            \"w-1\": indicator === \"line\",\r\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                              indicator === \"dashed\",\r\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                          }\r\n                        )}\r\n                        style={\r\n                          {\r\n                            \"--color-bg\": indicatorColor,\r\n                            \"--color-border\": indicatorColor,\r\n                          } as React.CSSProperties\r\n                        }\r\n                      />\r\n                    )\r\n                  )}\r\n                  <div\r\n                    className={cn(\r\n                      \"flex flex-1 justify-between leading-none\",\r\n                      nestLabel ? \"items-end\" : \"items-center\"\r\n                    )}\r\n                  >\r\n                    <div className=\"grid gap-1.5\">\r\n                      {nestLabel ? tooltipLabel : null}\r\n                      <span className=\"text-muted-foreground\">\r\n                        {itemConfig?.label || item.name}\r\n                      </span>\r\n                    </div>\r\n                    {item.value && (\r\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\r\n                        {item.value.toLocaleString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nfunction ChartLegendContent({\r\n  className,\r\n  hideIcon = false,\r\n  payload,\r\n  verticalAlign = \"bottom\",\r\n  nameKey,\r\n}: React.ComponentProps<\"div\"> &\r\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n    hideIcon?: boolean\r\n    nameKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  if (!payload?.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex items-center justify-center gap-4\",\r\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n        className\r\n      )}\r\n    >\r\n      {payload.map((item) => {\r\n        const key = `${nameKey || item.dataKey || \"value\"}`\r\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n        return (\r\n          <div\r\n            key={item.value}\r\n            className={cn(\r\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\r\n            )}\r\n          >\r\n            {itemConfig?.icon && !hideIcon ? (\r\n              <itemConfig.icon />\r\n            ) : (\r\n              <div\r\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                style={{\r\n                  backgroundColor: item.color,\r\n                }}\r\n              />\r\n            )}\r\n            {itemConfig?.label}\r\n          </div>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,qMAAA,CAAA,gBAAmB,CAA2B;AAEnE,SAAS;IACP,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OAMJ;IACC,MAAM,WAAW,qMAAA,CAAA,QAAW;IAC5B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,8OAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,8OAAC,mKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;AAEA,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;AAEA,MAAM,eAAe,uJAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EAQP;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,qMAAA,CAAA,UAAa,CAAC;QACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;YACjC,OAAO;QACT;QAEA,MAAM,CAAC,KAAK,GAAG;QACf,MAAM,MAAM,GAAG,YAAY,MAAM,WAAW,MAAM,QAAQ,SAAS;QACnE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;QAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;QAElB,IAAI,gBAAgB;YAClB,qBACE,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAC/B,eAAe,OAAO;;;;;;QAG7B;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,qBAAO,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;sBAAkB;;;;;;IAC7D,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,8OAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,8OAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;AAEA,MAAM,cAAc,sJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EAKN;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,8OAAC,WAAW,IAAI;;;;6CAEhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;AAEA,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 1342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ChevronDownIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 110.671 28\" {...props}><g xmlns=\"http://www.w3.org/2000/svg\"><rect width={110.071} height={27.4} x={0.304} y={0.3} fill=\"#FCFDFD\" stroke=\"#D5D5D5\" strokeWidth={0.6} rx={3.7} /><path fill=\"#2B3034\" fillOpacity={0.4} d=\"M29.274 13.99q0 1.125-.36 2.07c-.23.63-.57 1.16-1 1.62-.44.45-.96.8-1.57 1.05s-1.28.38-2.02.38c-.73 0-1.41-.13-2.01-.38q-.915-.375-1.56-1.05c-.44-.46-.77-.99-1.01-1.62a5.8 5.8 0 0 1-.36-2.07q0-1.125.36-2.07c.24-.63.57-1.17 1.01-1.62.43-.46.95-.81 1.56-1.07.6-.25 1.28-.37 2.01-.37.74 0 1.41.12 2.02.37.61.26 1.13.61 1.57 1.07.43.45.77.99 1 1.62q.36.945.36 2.07m-1.39 0c0-.62-.09-1.17-.25-1.66-.17-.49-.41-.9-.72-1.24s-.68-.6-1.12-.78a3.7 3.7 0 0 0-1.47-.28c-.53 0-1.02.09-1.46.28-.44.18-.81.44-1.13.78-.31.34-.54.75-.71 1.24s-.25 1.04-.25 1.66c0 .61.08 1.17.25 1.66.17.48.4.9.71 1.24.32.33.69.59 1.13.77s.93.27 1.46.27q.81 0 1.47-.27c.44-.18.81-.44 1.12-.77.31-.34.55-.76.72-1.24.16-.49.25-1.05.25-1.66m7.88-.82c-.04.05-.07.09-.11.12s-.09.04-.16.04-.14-.03-.23-.08c-.08-.06-.19-.13-.31-.2s-.27-.13-.46-.19c-.17-.06-.39-.09-.65-.09-.35 0-.66.06-.93.19-.26.12-.49.3-.67.53s-.31.52-.41.85c-.09.33-.13.7-.13 1.11 0 .43.05.81.15 1.15.1.33.23.61.41.84.18.22.4.4.65.52.26.11.54.17.86.17.31 0 .56-.03.75-.1.2-.08.36-.16.48-.25q.195-.135.33-.24a.4.4 0 0 1 .26-.11c.1 0 .18.04.23.12l.35.46c-.15.19-.33.35-.52.49-.2.13-.41.25-.64.34-.22.09-.46.15-.71.2-.24.04-.5.06-.75.06-.45 0-.86-.08-1.24-.25-.38-.16-.71-.4-.99-.7a3.5 3.5 0 0 1-.66-1.15c-.15-.45-.23-.97-.23-1.55 0-.53.07-1.01.21-1.46.15-.45.37-.84.65-1.16s.63-.58 1.04-.76c.42-.18.89-.27 1.43-.27.5 0 .94.08 1.31.24.39.16.73.39 1.02.68zm3.93 5.94c-.56 0-.99-.15-1.3-.47q-.45-.465-.45-1.35v-4.34h-.85c-.07 0-.14-.02-.19-.06a.29.29 0 0 1-.08-.21v-.5l1.17-.15.28-2.19c.02-.07.05-.12.09-.16.06-.05.12-.07.2-.07h.63v2.44h2.05v.9h-2.05v4.26c0 .3.07.52.22.66.14.15.33.22.56.22.13 0 .24-.02.33-.05.1-.04.19-.08.26-.12s.12-.08.17-.11c.05-.04.1-.06.13-.06q.105 0 .18.12l.36.6c-.21.2-.47.36-.77.47-.31.12-.62.17-.94.17m5.79-7.31c.51 0 .98.08 1.4.26.41.17.77.41 1.06.73s.52.7.67 1.16c.16.44.24.95.24 1.5 0 .56-.08 1.06-.24 1.51a3.2 3.2 0 0 1-.67 1.15c-.29.32-.65.56-1.06.74-.42.16-.89.25-1.4.25-.53 0-1-.09-1.42-.25a3.1 3.1 0 0 1-1.06-.74c-.3-.32-.52-.7-.68-1.15a4.7 4.7 0 0 1-.23-1.51c0-.55.08-1.06.23-1.5.16-.46.38-.84.68-1.16.29-.32.65-.56 1.06-.73.42-.18.89-.26 1.42-.26m0 6.32c.7 0 1.22-.23 1.56-.7.35-.47.52-1.12.52-1.96 0-.85-.17-1.5-.52-1.98-.34-.47-.86-.7-1.56-.7q-.54 0-.93.18c-.27.12-.49.3-.66.52-.18.23-.31.51-.39.85-.09.33-.13.71-.13 1.13 0 .84.17 1.49.52 1.96s.88.7 1.59.7m6.2-.83c.23.31.48.53.75.65.28.13.58.19.91.19.66 0 1.17-.23 1.53-.71.35-.47.53-1.17.53-2.1 0-.87-.16-1.51-.48-1.91-.31-.41-.76-.62-1.34-.62-.41 0-.76.09-1.06.28s-.58.45-.84.79zm0-4.36c.3-.34.64-.62 1.01-.83.38-.2.81-.3 1.3-.3.42 0 .79.08 1.12.23.34.16.62.39.86.7.23.3.41.66.53 1.09.13.43.19.91.19 1.45 0 .57-.07 1.1-.21 1.57s-.34.87-.61 1.21c-.26.33-.58.59-.96.77-.38.19-.8.28-1.27.28s-.86-.09-1.19-.26c-.31-.18-.59-.43-.83-.75l-.06.64c-.04.18-.15.27-.32.27h-.81V8.69h1.25zm11.33 1.75c0-.29-.04-.55-.13-.79-.07-.24-.19-.45-.35-.62a1.54 1.54 0 0 0-.57-.41c-.22-.1-.48-.15-.76-.15-.6 0-1.07.18-1.42.53-.35.34-.56.82-.65 1.44zm1.01 3.33c-.16.18-.34.35-.55.49-.22.13-.45.24-.7.33-.24.09-.49.16-.75.2s-.52.07-.78.07c-.49 0-.94-.08-1.36-.25-.41-.17-.77-.41-1.07-.73s-.53-.71-.7-1.19c-.17-.47-.25-1.01-.25-1.62 0-.49.07-.96.22-1.39.16-.42.38-.8.66-1.11.29-.32.63-.56 1.04-.74s.88-.27 1.39-.27c.42 0 .82.07 1.18.21s.67.35.93.62q.405.405.63.99c.15.4.22.84.22 1.35q0 .285-.06.39c-.04.07-.12.1-.24.1h-4.74c.01.45.08.84.18 1.17.11.33.27.61.46.83.2.22.43.38.7.5.28.1.58.16.91.16.32 0 .59-.04.81-.11.23-.07.42-.15.59-.24.16-.08.3-.16.4-.23.12-.07.21-.11.29-.11q.15 0 .24.12zm2.82-4.68c.23-.49.5-.86.83-1.13q.495-.42 1.2-.42c.15 0 .29.02.42.05.14.03.27.09.37.16l-.09.93c-.02.11-.09.17-.21.17-.06 0-.16-.01-.28-.04-.13-.03-.27-.04-.43-.04-.22 0-.42.03-.6.1-.17.06-.33.16-.47.29s-.26.28-.37.48c-.11.18-.21.4-.3.64V19h-1.25v-7.09h.71c.14 0 .23.02.28.08.06.05.09.14.11.26zM96.514 10.77l-4.05 4.02-4.05-4.02c-.31-.35-.63-.36-.95-.02-.32.33-.32.66 0 .98l4.52 4.55c.12.15.28.22.48.22s.36-.07.48-.22l4.52-4.55c.32-.32.32-.65 0-.98-.32-.34-.64-.33-.95.02\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,8OAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAkB,GAAG,KAAK;kBAAE,cAAA,8OAAC;YAAE,OAAM;;8BAA6B,8OAAC;oBAAK,OAAO;oBAAS,QAAQ;oBAAM,GAAG;oBAAO,GAAG;oBAAK,MAAK;oBAAU,QAAO;oBAAU,aAAa;oBAAK,IAAI;;;;;;8BAAO,8OAAC;oBAAK,MAAK;oBAAU,aAAa;oBAAK,GAAE;;;;;;;;;;;;;;;;;uCACjU", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/SymptomChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, CardContent, CardHeader } from \"@/components/ui/card\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { ChartContainer, ChartTooltip, ChartTooltipContent } from \"@/components/ui/chart\";\nimport { AreaChart, Area, XAxis, YAxis, CartesianGrid } from \"recharts\";\nimport ChevronDownIcon from \"@/components/icons/ChevronDownIcon\";\n\ninterface ChartDataPoint {\n  day: string;\n  value: number;\n  dayIndex: number;\n}\n\ninterface SymptomChartProps {\n  data: ChartDataPoint[];\n}\n\nconst chartConfig = {\n  value: {\n    label: \"Symptom Intensity\",\n    color: \"#3b82f6\",\n  },\n};\n\nexport function SymptomChart({ data }: SymptomChartProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"health-heading-lg\">Symptom Trend Visualization</h2>\n      \n      <Card className=\"health-card-shadow health-rounded\">\n        <CardHeader className=\"flex flex-row items-center justify-between pb-4\">\n          <p className=\"health-body-lg text-[#202224]\">Last 7 days</p>\n          <Select defaultValue=\"october\">\n            <SelectTrigger className=\"w-32 border-0 bg-transparent\">\n              <SelectValue />\n              <ChevronDownIcon width={16} height={16} color=\"#868686\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"october\">October</SelectItem>\n              <SelectItem value=\"november\">November</SelectItem>\n              <SelectItem value=\"december\">December</SelectItem>\n            </SelectContent>\n          </Select>\n        </CardHeader>\n        \n        <CardContent className=\"pt-0\">\n          <div className=\"flex flex-col space-y-4\">\n            <div className=\"flex justify-between text-sm text-[rgba(43,48,52,0.40)]\">\n              {data.map((point) => (\n                <span key={point.day}>{point.day}</span>\n              ))}\n            </div>\n            \n            <div className=\"h-64\">\n              <ChartContainer config={chartConfig} className=\"w-full h-full\">\n                <AreaChart data={data} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>\n                  <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n                  <XAxis \n                    dataKey=\"dayIndex\" \n                    axisLine={false}\n                    tickLine={false}\n                    tick={false}\n                  />\n                  <YAxis \n                    axisLine={false}\n                    tickLine={false}\n                    tick={{ fontSize: 14, fill: \"rgba(43,48,52,0.40)\" }}\n                    domain={[0, 8]}\n                    ticks={[1, 2, 3, 4, 5, 6, 7, 8]}\n                  />\n                  <ChartTooltip content={<ChartTooltipContent />} />\n                  <Area\n                    type=\"monotone\"\n                    dataKey=\"value\"\n                    stroke=\"#3b82f6\"\n                    strokeWidth={2}\n                    fill=\"rgba(59, 130, 246, 0.1)\"\n                    dot={{ fill: \"#3b82f6\", strokeWidth: 2, r: 4 }}\n                  />\n                </AreaChart>\n              </ChartContainer>\n            </div>\n            \n            <div className=\"flex justify-between text-sm text-[rgba(43,48,52,0.40)] px-5\">\n              {[10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120].map((num) => (\n                <span key={num}>{num}</span>\n              ))}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAkBA,MAAM,cAAc;IAClB,OAAO;QACL,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS,aAAa,EAAE,IAAI,EAAqB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,cAAa;;kDACnB,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,8OAAC,kIAAA,CAAA,cAAW;;;;;0DACZ,8OAAC,8IAAA,CAAA,UAAe;gDAAC,OAAO;gDAAI,QAAQ;gDAAI,OAAM;;;;;;;;;;;;kDAEhD,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAKnC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAC,sBACT,8OAAC;sDAAsB,MAAM,GAAG;2CAArB,MAAM,GAAG;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,iBAAc;wCAAC,QAAQ;wCAAa,WAAU;kDAC7C,cAAA,8OAAC,qJAAA,CAAA,YAAS;4CAAC,MAAM;4CAAM,QAAQ;gDAAE,KAAK;gDAAI,OAAO;gDAAI,QAAQ;gDAAI,MAAM;4CAAG;;8DACxE,8OAAC,6JAAA,CAAA,gBAAa;oDAAC,iBAAgB;oDAAM,QAAO;;;;;;8DAC5C,8OAAC,qJAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,UAAU;oDACV,UAAU;oDACV,MAAM;;;;;;8DAER,8OAAC,qJAAA,CAAA,QAAK;oDACJ,UAAU;oDACV,UAAU;oDACV,MAAM;wDAAE,UAAU;wDAAI,MAAM;oDAAsB;oDAClD,QAAQ;wDAAC;wDAAG;qDAAE;oDACd,OAAO;wDAAC;wDAAG;wDAAG;wDAAG;wDAAG;wDAAG;wDAAG;wDAAG;qDAAE;;;;;;8DAEjC,8OAAC,iIAAA,CAAA,eAAY;oDAAC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;;;;;;;;;;8DAC3C,8OAAC,oJAAA,CAAA,OAAI;oDACH,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;oDACb,MAAK;oDACL,KAAK;wDAAE,MAAM;wDAAW,aAAa;wDAAG,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;8CAMrD,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;wCAAK;wCAAK;qCAAI,CAAC,GAAG,CAAC,CAAC,oBACxD,8OAAC;sDAAgB;2CAAN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/symptom-analysis/HistoricalDataTable.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\nimport { formatDate } from \"@/app/symptomAnalysisMockData\";\n\ninterface HistoricalEntry {\n  dateLogged: Date;\n  severity: string;\n  aiSummary: string;\n}\n\ninterface HistoricalDataTableProps {\n  data: HistoricalEntry[];\n  analysisText: string;\n}\n\nexport function HistoricalDataTable({ data, analysisText }: HistoricalDataTableProps) {\n  return (\n    <div className=\"space-y-4\">\n      <h3 className=\"health-heading-sm\">AI Analysis Summary</h3>\n      \n      <div className=\"space-y-4\">\n        <p className=\"health-body-lg\">{analysisText}</p>\n        <p className=\"health-body-lg\">{analysisText}</p>\n        \n        <Card className=\"health-card-shadow rounded-lg\">\n          <CardContent className=\"p-0\">\n            <Table>\n              <TableHeader>\n                <TableRow className=\"border-b border-[#eff1f3]\">\n                  <TableHead className=\"health-body-lg text-[#191919] font-normal px-6 py-4\">\n                    Date Logged\n                  </TableHead>\n                  <TableHead className=\"health-body-lg text-[#191919] font-normal px-6 py-4\">\n                    Severity\n                  </TableHead>\n                  <TableHead className=\"health-body-lg text-[#191919] font-normal px-6 py-4\">\n                    Brief AI Summary\n                  </TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {data.map((entry, index) => (\n                  <TableRow \n                    key={index} \n                    className={`border-b border-[#eff1f3] ${index === data.length - 1 ? 'rounded-b-lg' : ''}`}\n                  >\n                    <TableCell className=\"health-button-text px-6 py-4\">\n                      {formatDate(entry.dateLogged)}\n                    </TableCell>\n                    <TableCell className=\"health-button-text px-6 py-4\">\n                      {entry.severity}\n                    </TableCell>\n                    <TableCell className=\"health-button-text px-6 py-4\">\n                      {entry.aiSummary}\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAiBO,SAAS,oBAAoB,EAAE,IAAI,EAAE,YAAY,EAA4B;IAClF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAElC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAkB;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAkB;;;;;;kCAE/B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsD;;;;;;8DAG3E,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsD;;;;;;8DAG3E,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsD;;;;;;;;;;;;;;;;;kDAK/E,8OAAC,iIAAA,CAAA,YAAS;kDACP,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,WAAW,CAAC,0BAA0B,EAAE,UAAU,KAAK,MAAM,GAAG,IAAI,iBAAiB,IAAI;;kEAEzF,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;;;;;kEAE9B,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,MAAM,QAAQ;;;;;;kEAEjB,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,MAAM,SAAS;;;;;;;+CAVb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBzB", "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ChevronRightIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 7.006 13.341\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" d=\"M.188 1.125a.66.66 0 0 1-.124-.73.665.665 0 0 1 .882-.337.7.7 0 0 1 .212.15l5.666 6a.67.67 0 0 1 0 .917l-5.666 6a.67.67 0 0 1-.727.173.67.67 0 0 1-.385-.867.7.7 0 0 1 .142-.221l5.235-5.543z\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,8OAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAoB,GAAG,KAAK;kBAAE,cAAA,8OAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,GAAE;;;;;;;;;;;uCAChM", "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/%28dashboard%29/symptom_analysis/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { B<PERSON><PERSON>rumb, BreadcrumbItem, Bread<PERSON>rumbLink, BreadcrumbList } from \"@/components/ui/breadcrumb\";\nimport { SymptomOverviewCard } from \"@/components/symptom-analysis/SymptomOverviewCard\";\nimport { AIInsightSection } from \"@/components/symptom-analysis/AIInsightSection\";\nimport { PotentialConditionsCard } from \"@/components/symptom-analysis/PotentialConditionsCard\";\nimport { RecommendationsSection } from \"@/components/symptom-analysis/RecommendationsSection\";\nimport { QuickActionsSection } from \"@/components/symptom-analysis/QuickActionsSection\";\nimport { SymptomChart } from \"@/components/symptom-analysis/SymptomChart\";\nimport { HistoricalDataTable } from \"@/components/symptom-analysis/HistoricalDataTable\";\nimport ChevronRightIcon from \"@/components/icons/ChevronRightIcon\";\nimport { mockRootProps, formatDate, formatTime } from \"@/app/symptomAnalysisMockData\";\n\nexport default function SymptomAnalysisPage() {\n  const {\n    symptomData,\n    aiInsights,\n    potentialConditions,\n    healthAlerts,\n    recommendations,\n    chartData,\n    historicalData\n  } = mockRootProps;\n\n  return (\n    <div className=\"p-6 space-y-6 max-w-7xl mx-auto\">\n      {/* Breadcrumb Navigation */}\n      <Breadcrumb>\n        <BreadcrumbList className=\"flex items-center gap-1\">\n          <BreadcrumbItem>\n            <BreadcrumbLink href=\"/\" className=\"health-body-lg text-[#868686]\">\n              User Dashboard\n            </BreadcrumbLink>\n          </BreadcrumbItem>\n          <ChevronRightIcon width={7} height={13} color=\"#868686\" />\n          <BreadcrumbItem>\n            <span className=\"health-body-lg text-[#868686]\">Recent Symptom Analysis</span>\n          </BreadcrumbItem>\n        </BreadcrumbList>\n      </Breadcrumb>\n\n      {/* Main Heading */}\n      <h1 className=\"health-heading-lg\">{symptomData.type}</h1>\n\n      {/* Symptom Overview Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5\">\n        <SymptomOverviewCard\n          title={symptomData.type}\n          value={`Severity: ${symptomData.severity}`}\n        />\n        <SymptomOverviewCard\n          title=\"Detected Date\"\n          value={formatDate(symptomData.detectedDate)}\n        />\n        <SymptomOverviewCard\n          title=\"Detected Time\"\n          value={formatTime(symptomData.detectedTime)}\n        />\n        <SymptomOverviewCard\n          title=\"Associated Factors\"\n          value={symptomData.associatedFactors.join(\", \")}\n        />\n      </div>\n\n      {/* AI Insight Section */}\n      <AIInsightSection\n        heartRate={aiInsights.heartRate}\n        airQuality={aiInsights.airQuality}\n        sleepHours={aiInsights.sleepHours}\n        analysisText={aiInsights.analysisText}\n      />\n\n      {/* Potential Conditions */}\n      <PotentialConditionsCard conditions={potentialConditions} />\n\n      {/* Recommendations and Health Alerts */}\n      <RecommendationsSection\n        recommendations={recommendations}\n        healthAlerts={healthAlerts}\n      />\n\n      {/* Quick Actions */}\n      <QuickActionsSection />\n\n      {/* Symptom Trend Chart */}\n      <SymptomChart data={chartData} />\n\n      {/* Historical Data Table */}\n      <HistoricalDataTable\n        data={historicalData}\n        analysisText={aiInsights.analysisText}\n      />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EACJ,WAAW,EACX,UAAU,EACV,mBAAmB,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,cAAc,EACf,GAAG,qIAAA,CAAA,gBAAa;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBAAC,WAAU;;sCACxB,8OAAC,sIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,MAAK;gCAAI,WAAU;0CAAgC;;;;;;;;;;;sCAIrE,8OAAC,+IAAA,CAAA,UAAgB;4BAAC,OAAO;4BAAG,QAAQ;4BAAI,OAAM;;;;;;sCAC9C,8OAAC,sIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;0BAMtD,8OAAC;gBAAG,WAAU;0BAAqB,YAAY,IAAI;;;;;;0BAGnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gKAAA,CAAA,sBAAmB;wBAClB,OAAO,YAAY,IAAI;wBACvB,OAAO,CAAC,UAAU,EAAE,YAAY,QAAQ,EAAE;;;;;;kCAE5C,8OAAC,gKAAA,CAAA,sBAAmB;wBAClB,OAAM;wBACN,OAAO,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD,EAAE,YAAY,YAAY;;;;;;kCAE5C,8OAAC,gKAAA,CAAA,sBAAmB;wBAClB,OAAM;wBACN,OAAO,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD,EAAE,YAAY,YAAY;;;;;;kCAE5C,8OAAC,gKAAA,CAAA,sBAAmB;wBAClB,OAAM;wBACN,OAAO,YAAY,iBAAiB,CAAC,IAAI,CAAC;;;;;;;;;;;;0BAK9C,8OAAC,6JAAA,CAAA,mBAAgB;gBACf,WAAW,WAAW,SAAS;gBAC/B,YAAY,WAAW,UAAU;gBACjC,YAAY,WAAW,UAAU;gBACjC,cAAc,WAAW,YAAY;;;;;;0BAIvC,8OAAC,oKAAA,CAAA,0BAAuB;gBAAC,YAAY;;;;;;0BAGrC,8OAAC,mKAAA,CAAA,yBAAsB;gBACrB,iBAAiB;gBACjB,cAAc;;;;;;0BAIhB,8OAAC,gKAAA,CAAA,sBAAmB;;;;;0BAGpB,8OAAC,yJAAA,CAAA,eAAY;gBAAC,MAAM;;;;;;0BAGpB,8OAAC,gKAAA,CAAA,sBAAmB;gBAClB,MAAM;gBACN,cAAc,WAAW,YAAY;;;;;;;;;;;;AAI7C", "debugId": null}}]}