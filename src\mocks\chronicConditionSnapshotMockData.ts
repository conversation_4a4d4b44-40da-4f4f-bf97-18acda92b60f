// import enums.ts if any

// Data passed as props to the root component
export const mockRootProps = {
  patientData: {
    condition: "Migraines" as const,
    diagnosisDate: "2024-03-01",
    lastReview: "2025-06-28", 
    status: "improving" as const
  },
  energyData: [
    { day: "Mon", allocatedEnergy: 30, usedEnergy: 25 },
    { day: "Tue", allocatedEnergy: 35, usedEnergy: 40 },
    { day: "Wed", allocatedEnergy: 40, usedEnergy: 35 },
    { day: "Thu", allocatedEnergy: 50, usedEnergy: 45 },
    { day: "Fri", allocatedEnergy: 45, usedEnergy: 35 },
    { day: "Sat", allocatedEnergy: 35, usedEnergy: 30 },
    { day: "Sun", allocatedEnergy: 55, usedEnergy: 50 }
  ],
  activityGuidance: {
    recommendedLevel: "Light Activity" as const,
    description: "Focus on gentle movement and avoid high-intensity activities",
    restBreaks: "15 min every 2 hours",
    restDescription: "Regular breaks help prevent energy depletion"
  }
};