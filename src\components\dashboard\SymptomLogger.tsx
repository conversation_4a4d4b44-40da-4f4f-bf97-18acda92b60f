"use client";

import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";

interface SymptomLoggerProps {
  onSubmit?: (symptom: string) => void;
}

export function SymptomLogger({ onSubmit }: SymptomLoggerProps) {
  const [symptom, setSymptom] = useState("");

  const handleSubmit = () => {
    if (symptom.trim()) {
      onSubmit?.(symptom);
      setSymptom("");
    }
  };

  return (
    <div className="space-y-2">
      <p className="health-heading-md">Symptom Description:</p>
      <Textarea
        placeholder="Enter symptom....."
        value={symptom}
        onChange={(e) => setSymptom(e.target.value)}
        className="min-h-24 resize-none"
      />
      <div className="flex justify-end">
        <Button 
          onClick={handleSubmit}
          className="bg-health-chart-teal hover:bg-health-chart-teal/90 text-white font-bold px-6 py-2"
          style={{ backgroundColor: 'var(--health-chart-teal)' }}
        >
          Submit Log
        </Button>
      </div>
    </div>
  );
}