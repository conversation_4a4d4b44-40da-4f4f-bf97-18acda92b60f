// Mock data for AI-Powered Reminders page
import { MedicationFrequency, AppointmentType } from './enums';

export const mockMedications = [
  {
    id: "med-1",
    name: "Lisnioril",
    dosage: "200mg",
    time: new Date("2024-01-01T08:00:00"),
    frequency: MedicationFrequency.TWICE
  },
  {
    id: "med-2", 
    name: "Amoxicillin",
    dosage: "500mg",
    time: new Date("2024-01-01T09:00:00"),
    frequency: MedicationFrequency.ONCE
  },
  {
    id: "med-3",
    name: "Omeprazole", 
    dosage: "20mg",
    time: new Date("2024-01-01T08:30:00"),
    frequency: MedicationFrequency.ONCE
  },
  {
    id: "med-4",
    name: "Atorvastatin",
    dosage: "20mg", 
    time: new Date("2024-01-01T19:00:00"),
    frequency: MedicationFrequency.ONCE
  },
  {
    id: "med-5",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    dosage: "10mg",
    time: new Date("2024-01-01T18:00:00"), 
    frequency: MedicationFrequency.ONCE
  },
  {
    id: "med-6",
    name: "<PERSON><PERSON><PERSON>",
    dosage: "500mg",
    time: new Date("2024-01-01T12:00:00"),
    frequency: MedicationFrequency.TWICE
  },
  {
    id: "med-7",
    name: "Gabapentin",
    dosage: "300mg",
    time: new Date("2024-01-01T21:30:00"),
    frequency: MedicationFrequency.THREE_TIMES
  },
  {
    id: "med-8",
    name: "Ibuprofen", 
    dosage: "200mg",
    time: new Date("2024-01-01T10:00:00"),
    frequency: MedicationFrequency.TWICE
  },
  {
    id: "med-9",
    name: "Amlodipine",
    dosage: "5mg",
    time: new Date("2024-01-01T13:00:00"),
    frequency: MedicationFrequency.ONCE
  },
  {
    id: "med-10",
    name: "Simvastatin",
    dosage: "40mg", 
    time: new Date("2024-01-01T21:00:00"),
    frequency: MedicationFrequency.ONCE
  },
  {
    id: "med-11",
    name: "Lisinopril",
    dosage: "10mg",
    time: new Date("2024-01-01T08:00:00"),
    frequency: MedicationFrequency.ONCE
  }
];

export const mockAppointments = [
  {
    id: "appt-1",
    type: AppointmentType.CONSULTATION,
    dateTime: new Date("2024-01-01T20:00:00"),
    provider: "Dr Adewumi"
  },
  {
    id: "appt-2",
    type: AppointmentType.CONSULTATION, 
    dateTime: new Date("2024-01-01T20:00:00"),
    provider: "Dr Adewumi"
  },
  {
    id: "appt-3",
    type: AppointmentType.ONLINE,
    dateTime: new Date("2024-01-05T16:00:00"),
    provider: "Dr Brown"
  }
];

export const mockRootProps = {
  medications: mockMedications,
  appointments: mockAppointments
};